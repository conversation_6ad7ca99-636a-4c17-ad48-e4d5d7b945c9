import React, { useState, useEffect } from 'react'
import axios from 'axios'

function App() {
  const [servers, setServers] = useState([])
  const [firecrawlEnabled, setFirecrawlEnabled] = useState(false)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    checkServerStatus()
  }, [])

  const checkServerStatus = async () => {
    try {
      const response = await axios.get('http://localhost:3002/health')
      setFirecrawlEnabled(response.status === 200)
    } catch (error) {
      setFirecrawlEnabled(false)
    }
  }

  const enableFirecrawl = async () => {
    setLoading(true)
    try {
      await axios.post('http://localhost:3002/enable')
      setFirecrawlEnabled(true)
      alert('Firecrawl MCP Server enabled successfully!')
    } catch (error) {
      alert('Failed to enable Firecrawl MCP Server: ' + error.message)
    }
    setLoading(false)
  }

  return (
    <div style={{ padding: '20px', fontFamily: 'Arial, sans-serif' }}>
      <h1>Docker MCP Toolkit</h1>
      <div style={{ marginBottom: '20px' }}>
        <h2>MCP Servers</h2>
        <div style={{ border: '1px solid #ccc', padding: '10px', borderRadius: '5px' }}>
          <h3>🔥 Firecrawl MCP Server</h3>
          <p>Status: {firecrawlEnabled ? '✅ Enabled' : '❌ Disabled'}</p>
          {!firecrawlEnabled && (
            <button 
              onClick={enableFirecrawl}
              disabled={loading}
              style={{
                background: '#007bff',
                color: 'white',
                border: 'none',
                padding: '10px 20px',
                borderRadius: '5px',
                cursor: loading ? 'not-allowed' : 'pointer',
                opacity: loading ? 0.6 : 1
              }}
            >
              {loading ? 'Enabling...' : 'Enable Firecrawl'}
            </button>
          )}
          {firecrawlEnabled && (
            <div style={{ marginTop: '10px', color: 'green' }}>
              ✓ Server is ready to crawl websites
            </div>
          )}
        </div>
      </div>
      <div>
        <h2>Configuration</h2>
        <p>Configuration file: .codes.json</p>
        <pre style={{ background: '#f5f5f5', padding: '10px', borderRadius: '5px' }}>
          {JSON.stringify({ project: "webcafeshop", description: "FireCrawl configuration file", crawlDepth: 2 }, null, 2)}
        </pre>
      </div>
      <div style={{ marginTop: '20px' }}>
        <h2>Quick Actions</h2>
        <button 
          onClick={checkServerStatus}
          style={{
            background: '#28a745',
            color: 'white',
            border: 'none',
            padding: '8px 16px',
            borderRadius: '5px',
            cursor: 'pointer',
            marginRight: '10px'
          }}
        >
          Refresh Status
        </button>
      </div>
    </div>
  )
}

export default App