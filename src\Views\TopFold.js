import React from "react";
import webcafelogo from "../assets/images/logo512.png";
import MenuSection from "../components/MenuSection";

/**
 * Hero section displaying the main brand message and logo.
 */
const TopFold = () => {
  return (
    <section className="relative min-h-screen bg-gradient-to-br from-white via-primary-50 to-secondary-50 overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0 bg-grid-pattern opacity-5"></div>
      <div className="absolute top-0 right-0 w-1/2 h-1/2 bg-gradient-to-bl from-secondary-100 to-transparent rounded-bl-full opacity-30"></div>
      <div className="absolute bottom-0 left-0 w-1/3 h-1/3 bg-gradient-to-tr from-primary-100 to-transparent rounded-tr-full opacity-30"></div>
      
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 sm:py-24 lg:py-32">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-16 items-center min-h-[80vh]">
          {/* Text Content */}
          <div
            className="text-center lg:text-left space-y-8 animate-fade-in"
            style={{ willChange: 'opacity, transform' }}
          >
            <div className="space-y-2">
              <p className="text-sm sm:text-base font-semibold text-gray-600 uppercase tracking-widest">
                Webcafe - automate <span className="font-bold text-primary-600">everything</span>
              </p>
              <div className="h-1 w-16 bg-gradient-to-r from-primary-500 to-secondary-500 mx-auto lg:mx-0 rounded-full"></div>
            </div>
            
            <div className="space-y-4">
              <h1 className="text-4xl sm:text-5xl lg:text-6xl xl:text-7xl font-bold text-gray-900 leading-tight">
                Holding{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-primary-600 to-secondary-600 animate-bounce-gentle">
                  Company.
                </span>
              </h1>
              
              <h1 className="text-3xl sm:text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 leading-tight">
                AI{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-secondary-600 to-accent-600">
                  Investor.
                </span>
              </h1>
              
              <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 leading-tight">
                WebCafeShop{" "}
                <span className="text-transparent bg-clip-text bg-gradient-to-r from-accent-600 to-primary-600">
                  AI.
                </span>
              </h1>
            </div>
            
            {/* Call to Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start pt-8">
              <button 
                onClick={() => document.getElementById('features')?.scrollIntoView({behavior:'smooth'})}
                className="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-white bg-gradient-to-r from-primary-600 to-secondary-600 rounded-xl shadow-medium hover:shadow-strong transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                <span className="relative z-10">Get Started</span>
                <div className="absolute inset-0 bg-gradient-to-r from-primary-700 to-secondary-700 rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                <svg className="ml-2 w-5 h-5 transition-transform duration-300 group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </button>
              
              <a href="#features" className="inline-block">
                <button className="group inline-flex items-center justify-center px-8 py-4 text-lg font-semibold text-gray-700 bg-white border-2 border-gray-300 rounded-xl shadow-soft hover:shadow-medium hover:border-primary-300 transform hover:scale-105 transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2">
                  Try Now
                  <svg className="ml-2 w-5 h-5 transition-transform duration-300 group-hover:rotate-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                </button>
              </a>
            </div>
          </div>
          
          {/* Logo Section */}
          <div className="flex justify-center lg:justify-end items-center">
            <div className="relative">
              {/* Decorative rings */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary-200 to-secondary-200 rounded-full animate-pulse opacity-20 scale-110"></div>
              <div className="absolute inset-0 bg-gradient-to-r from-secondary-200 to-accent-200 rounded-full animate-pulse opacity-20 scale-125 animation-delay-1000"></div>
              
              {/* Logo container */}
              <div className="relative bg-white rounded-full p-8 shadow-strong">
                <img 
                  src={webcafelogo} 
                  alt="Webcafe Logo" 
                  className="w-64 h-64 sm:w-80 sm:h-80 lg:w-96 lg:h-96 object-contain filter drop-shadow-xl animate-bounce-gentle"
                />
              </div>
            </div>
          </div>
        </div>
        
        {/* Scroll indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="flex flex-col items-center space-y-2">
            <span className="text-sm text-gray-500 font-medium">Scroll to explore</span>
            <svg className="w-6 h-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </div>
      </div>
      
      {/* Menu Section */}
      <MenuSection />
    </section>
  );
};

export default TopFold;
