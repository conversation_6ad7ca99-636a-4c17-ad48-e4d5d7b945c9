import React from "react";
import TopFold from "./TopFold";

/**
 * Displays the main landing page after a successful login.
 */
const HomePage = () => (
  <div className="min-h-screen bg-white">
    <TopFold />
    
    {/* Additional sections can be added here in the future */}
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            Welcome to WebCafeShop
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Experience the future of automation and AI-powered solutions. 
            Our platform combines cutting-edge technology with intuitive design 
            to help you achieve your goals faster and more efficiently.
          </p>
        </div>
        
        <div className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Feature cards */}
          {[
            {
              title: "AI-Powered Automation",
              description: "Leverage advanced AI to automate complex workflows and processes.",
              icon: "🤖"
            },
            {
              title: "Smart Investment Tools",
              description: "Make informed decisions with our intelligent investment analysis.",
              icon: "📊"
            },
            {
              title: "Seamless Integration",
              description: "Connect with your existing tools and platforms effortlessly.",
              icon: "🔗"
            }
          ].map((feature, index) => (
            <div key={index} className="bg-white rounded-xl shadow-soft p-6 hover:shadow-medium transition-shadow duration-300">
              <div className="text-4xl mb-4">{feature.icon}</div>
              <h3 className="text-xl font-semibold text-gray-900 mb-2">{feature.title}</h3>
              <p className="text-gray-600">{feature.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  </div>
);

export default HomePage;
