{"name": "webcafeshop", "version": "0.1.0", "private": true, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "cra-template": "1.2.0", "firebase": "^11.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.6.2", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"autoprefixer": "^10.4.21", "react-scripts": "^5.0.1", "tailwindcss": "^3.4.17"}}