{"name": "webcafeshop", "version": "0.1.0", "private": true, "dependencies": {"@tailwindcss/postcss": "^4.1.11", "cra-template": "1.2.0", "firebase": "^11.2.0", "react": "^19.0.0", "react-dom": "^19.0.0", "react-router-dom": "^7.6.2", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:automation": "react-scripts test --watchAll=false --coverage --testResultsProcessor=./jest-automation.js", "test:ci": "npm run test:automation && node jest-automation.js global-teardown", "eject": "react-scripts eject", "task-manager": "node task-manager.js", "webhook-server": "node webhook-handler.js start"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "autoprefixer": "^10.4.21", "express": "^5.1.0", "intersection-observer": "^0.12.2", "react-scripts": "^5.0.1", "tailwindcss": "^3.4.17"}}