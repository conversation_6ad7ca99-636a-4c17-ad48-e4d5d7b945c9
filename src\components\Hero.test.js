import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Hero from './Hero';
import { renderWithRouter } from '../utils/testUtils';

describe('Hero Component', () => {
  let user;

  beforeEach(() => {
    user = userEvent.setup();
    jest.clearAllMocks();
  });

  describe('Rendering Tests', () => {
    test('renders without crashing', () => {
      render(<Hero />);
      expect(screen.getByText(/WebCafe AI/i)).toBeInTheDocument();
    });

    test('renders main heading', () => {
      render(<Hero />);
      
      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toBeInTheDocument();
      expect(heading).toHaveTextContent('WebCafe AI');
    });

    test('renders subtitle text', () => {
      render(<Hero />);
      
      expect(screen.getByText(/Brew your boldest ideas/i)).toBeInTheDocument();
    });

    test('renders call-to-action button', () => {
      render(<Hero />);
      
      const ctaButton = screen.getByRole('link', { name: /try now/i });
      expect(ctaButton).toBeInTheDocument();
    });
  });

  describe('Styling Tests', () => {
    test('applies glassmorphic container styles', () => {
      render(<Hero />);
      
      const container = screen.getByText(/WebCafe AI/i).closest('div');
      expect(container).toHaveClass(
        'backdrop-blur-md',
        'bg-white/10',
        'rounded-lg',
        'shadow-strong',
        'border',
        'border-white/20'
      );
    });

    test('heading has proper styling', () => {
      render(<Hero />);
      
      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toHaveClass(
        'text-4xl',
        'md:text-5xl',
        'font-bold',
        'text-white',
        'mb-4'
      );
    });

    test('subtitle has proper styling', () => {
      render(<Hero />);
      
      const subtitle = screen.getByText(/Brew your boldest ideas/i);
      expect(subtitle).toHaveClass(
        'text-lg',
        'md:text-xl',
        'text-gray-200',
        'mb-8'
      );
    });

    test('container has proper layout styles', () => {
      render(<Hero />);
      
      const container = screen.getByText(/WebCafe AI/i).closest('div');
      expect(container).toHaveClass(
        'p-8',
        'md:p-12',
        'max-w-md',
        'mx-auto',
        'text-center'
      );
    });
  });

  describe('Interactive Elements Tests', () => {
    test('Try Now button has correct href', () => {
      render(<Hero />);
      
      const tryNowButton = screen.getByRole('link', { name: /try now/i });
      expect(tryNowButton).toHaveAttribute('href', '#features');
    });

    test('button has proper hover styles', () => {
      render(<Hero />);
      
      const tryNowButton = screen.getByRole('link', { name: /try now/i });
      expect(tryNowButton).toHaveClass(
        'hover:bg-white/30',
        'hover:scale-105',
        'transition-all',
        'duration-300'
      );
    });

    test('button has proper styling', () => {
      render(<Hero />);
      
      const tryNowButton = screen.getByRole('link', { name: /try now/i });
      expect(tryNowButton).toHaveClass(
        'inline-block',
        'bg-white/20',
        'backdrop-blur-sm',
        'text-white',
        'font-semibold',
        'py-3',
        'px-8',
        'rounded-lg',
        'border',
        'border-white/30'
      );
    });
  });

  describe('Animation Tests', () => {
    test('button has scale transform on hover', () => {
      render(<Hero />);
      
      const tryNowButton = screen.getByRole('link', { name: /try now/i });
      expect(tryNowButton).toHaveClass('hover:scale-105');
    });

    test('button has proper transition timing', () => {
      render(<Hero />);
      
      const tryNowButton = screen.getByRole('link', { name: /try now/i });
      expect(tryNowButton).toHaveClass('transition-all', 'duration-300');
    });

    test('backdrop blur effects are applied', () => {
      render(<Hero />);
      
      const container = screen.getByText(/WebCafe AI/i).closest('div');
      expect(container).toHaveClass('backdrop-blur-md');
      
      const button = screen.getByRole('link', { name: /try now/i });
      expect(button).toHaveClass('backdrop-blur-sm');
    });
  });

  describe('Accessibility Tests', () => {
    test('has proper heading structure', () => {
      render(<Hero />);
      
      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toBeInTheDocument();
      expect(heading).toHaveTextContent('WebCafe AI');
    });

    test('link has proper accessible text', () => {
      render(<Hero />);
      
      const link = screen.getByRole('link', { name: /try now/i });
      expect(link).toBeInTheDocument();
      expect(link).toHaveTextContent('Try Now');
    });

    test('has proper semantic structure', () => {
      render(<Hero />);
      
      // Check that content is properly structured
      const heading = screen.getByRole('heading');
      const link = screen.getByRole('link');
      
      expect(heading).toBeInTheDocument();
      expect(link).toBeInTheDocument();
    });
  });

  describe('Responsive Design Tests', () => {
    test('uses responsive text sizing', () => {
      render(<Hero />);
      
      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toHaveClass('text-4xl', 'md:text-5xl');
      
      const subtitle = screen.getByText(/Brew your boldest ideas/i);
      expect(subtitle).toHaveClass('text-lg', 'md:text-xl');
    });

    test('uses responsive padding', () => {
      render(<Hero />);
      
      const container = screen.getByText(/WebCafe AI/i).closest('div');
      expect(container).toHaveClass('p-8', 'md:p-12');
    });

    test('has responsive max-width', () => {
      render(<Hero />);
      
      const container = screen.getByText(/WebCafe AI/i).closest('div');
      expect(container).toHaveClass('max-w-md');
    });
  });

  describe('Visual Effects Tests', () => {
    test('applies shadow effects', () => {
      render(<Hero />);
      
      const container = screen.getByText(/WebCafe AI/i).closest('div');
      expect(container).toHaveClass('shadow-strong');
    });

    test('applies border effects', () => {
      render(<Hero />);
      
      const container = screen.getByText(/WebCafe AI/i).closest('div');
      expect(container).toHaveClass('border', 'border-white/20');
      
      const button = screen.getByRole('link', { name: /try now/i });
      expect(button).toHaveClass('border', 'border-white/30');
    });

    test('applies proper opacity effects', () => {
      render(<Hero />);
      
      const container = screen.getByText(/WebCafe AI/i).closest('div');
      expect(container).toHaveClass('bg-white/10');
      
      const button = screen.getByRole('link', { name: /try now/i });
      expect(button).toHaveClass('bg-white/20');
    });
  });

  describe('User Interaction Tests', () => {
    test('button responds to clicks', async () => {
      render(<Hero />);
      
      const button = screen.getByRole('link', { name: /try now/i });
      
      // Since it's an anchor link, we mainly test that it's clickable
      await user.click(button);
      
      // The href should still be correct after interaction
      expect(button).toHaveAttribute('href', '#features');
    });

    test('button is focusable', async () => {
      render(<Hero />);
      
      const button = screen.getByRole('link', { name: /try now/i });
      
      await user.tab();
      expect(button).toHaveFocus();
    });
  });

  describe('Content Tests', () => {
    test('displays correct brand name', () => {
      render(<Hero />);
      
      expect(screen.getByText('WebCafe AI')).toBeInTheDocument();
    });

    test('displays correct tagline', () => {
      render(<Hero />);
      
      expect(screen.getByText('Brew your boldest ideas')).toBeInTheDocument();
    });

    test('displays correct call-to-action text', () => {
      render(<Hero />);
      
      expect(screen.getByText('Try Now')).toBeInTheDocument();
    });
  });

  describe('Layout Tests', () => {
    test('centers content properly', () => {
      render(<Hero />);
      
      const container = screen.getByText(/WebCafe AI/i).closest('div');
      expect(container).toHaveClass('mx-auto', 'text-center');
    });

    test('has proper spacing between elements', () => {
      render(<Hero />);
      
      const heading = screen.getByRole('heading', { level: 1 });
      expect(heading).toHaveClass('mb-4');
      
      const subtitle = screen.getByText(/Brew your boldest ideas/i);
      expect(subtitle).toHaveClass('mb-8');
    });
  });
});