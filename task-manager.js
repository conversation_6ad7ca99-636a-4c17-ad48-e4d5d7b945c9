#!/usr/bin/env node

/**
 * WebCafeShop Task Manager
 * Interactive CLI tool for managing project tasks
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const TASK_FILE = 'augment.taskplan.json';

class TaskManager {
  constructor() {
    this.tasks = this.loadTasks();
    this.rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    });
  }

  loadTasks() {
    try {
      const data = fs.readFileSync(TASK_FILE, 'utf8');
      return JSON.parse(data);
    } catch (error) {
      console.error('Error loading tasks:', error.message);
      return this.getDefaultTasks();
    }
  }

  saveTasks() {
    try {
      // Update progress statistics
      this.tasks.completedTasks = this.tasks.tasks.filter(t => t.status === 'completed').length;
      this.tasks.totalTasks = this.tasks.tasks.length;
      this.tasks.progress = Math.round((this.tasks.completedTasks / this.tasks.totalTasks) * 100) + '%';

      fs.writeFileSync(TASK_FILE, JSON.stringify(this.tasks, null, 2));
      console.log('✅ Tasks saved successfully!');
    } catch (error) {
      console.error('❌ Error saving tasks:', error.message);
    }
  }

  /**
   * Update task status programmatically (for automation)
   * @param {Object} params - Task update parameters
   * @param {string} params.taskName - Name or ID of the task to update
   * @param {string} params.status - New status (completed, in-progress, todo, blocked)
   * @param {string} params.completedBy - Optional: who/what completed the task
   * @returns {boolean} - Success status
   */
  updateTaskStatus({ taskName, status, completedBy = 'automation' }) {
    try {
      // Find task by name or ID
      const task = this.tasks.tasks.find(t =>
        t.title.toLowerCase().includes(taskName.toLowerCase()) ||
        t.id === taskName
      );

      if (!task) {
        console.error(`❌ Task not found: ${taskName}`);
        return false;
      }

      const oldStatus = task.status;
      task.status = status;

      // Add completion metadata for automation tracking
      if (status === 'completed') {
        task.completedAt = new Date().toISOString();
        task.completedBy = completedBy;
        task.completed = true;
      }

      this.saveTasks();
      console.log(`✅ Task "${task.title}" updated from ${oldStatus} to ${status} by ${completedBy}`);

      // Update augment memory if task is completed
      if (status === 'completed') {
        this.updateAugmentMemory(task.id, status);
      }

      return true;
    } catch (error) {
      console.error('❌ Error updating task status:', error.message);
      return false;
    }
  }

  /**
   * Update augment memory with task completion status
   * @param {string} taskId - Task ID to update
   * @param {string} status - Task status
   */
  updateAugmentMemory(taskId, status) {
    try {
      const task = this.tasks.tasks.find(t => t.id === taskId);
      if (!task) {
        console.error(`❌ Task not found for augment memory update: ${taskId}`);
        return;
      }

      // Inject completion status into task object
      if (status === 'completed') {
        task.completed = true;
        task.augmentMemory = {
          completedAt: new Date().toISOString(),
          automationTriggered: true,
          phase: this.tasks.phase,
          progress: this.tasks.progress
        };
      }

      this.saveTasks();
      console.log(`🧠 Augment memory updated for task: ${task.title}`);
    } catch (error) {
      console.error('❌ Error updating augment memory:', error.message);
    }
  }

  getDefaultTasks() {
    return {
      project: "WebCafeShop - Coffee Menu System",
      phase: "Implementation",
      tasks: [],
      totalTasks: 0,
      completedTasks: 0,
      progress: "0%"
    };
  }

  displayTasks() {
    console.log('\n📋 WebCafeShop Task Status\n');
    console.log(`Project: ${this.tasks.project}`);
    console.log(`Phase: ${this.tasks.phase}`);
    console.log(`Progress: ${this.tasks.progress} (${this.tasks.completedTasks}/${this.tasks.totalTasks})\n`);

    const statusEmoji = {
      'completed': '✅',
      'in-progress': '🔄',
      'todo': '⏳',
      'blocked': '🚫'
    };

    this.tasks.tasks.forEach((task, index) => {
      const emoji = statusEmoji[task.status] || '❓';
      console.log(`${index + 1}. ${emoji} ${task.title}`);
      console.log(`   Status: ${task.status.toUpperCase()}`);
      console.log(`   Priority: ${task.priority.toUpperCase()}`);
      console.log(`   Time: ${task.estimatedTime}`);
      if (task.dependencies.length > 0) {
        console.log(`   Dependencies: ${task.dependencies.join(', ')}`);
      }
      console.log(`   Files: ${task.files.join(', ')}`);
      console.log(`   Description: ${task.description}\n`);
    });
  }

  async updateTaskStatusInteractive() {
    this.displayTasks();

    const taskNumber = await this.question('Enter task number to update (or 0 to cancel): ');
    const taskIndex = parseInt(taskNumber) - 1;

    if (taskNumber === '0') return;

    if (taskIndex < 0 || taskIndex >= this.tasks.tasks.length) {
      console.log('❌ Invalid task number!');
      return;
    }

    const task = this.tasks.tasks[taskIndex];
    console.log(`\nUpdating: ${task.title}`);
    console.log('Status options: completed, in-progress, todo, blocked');

    const newStatus = await this.question('Enter new status: ');

    if (['completed', 'in-progress', 'todo', 'blocked'].includes(newStatus)) {
      // Use the new automated method for consistency
      this.updateTaskStatus({
        taskName: task.id,
        status: newStatus,
        completedBy: 'user-interactive'
      });
    } else {
      console.log('❌ Invalid status!');
    }
  }

  async addNewTask() {
    console.log('\n➕ Adding New Task\n');
    
    const title = await this.question('Task title: ');
    const description = await this.question('Task description: ');
    const priority = await this.question('Priority (high/medium/low): ');
    const estimatedTime = await this.question('Estimated time (e.g., 30min, 2h): ');
    const files = await this.question('Files involved (comma-separated): ');
    const dependencies = await this.question('Dependencies (comma-separated task IDs, or press enter for none): ');

    const newTask = {
      id: this.generateTaskId(title),
      title,
      description,
      status: 'todo',
      priority: ['high', 'medium', 'low'].includes(priority) ? priority : 'medium',
      files: files.split(',').map(f => f.trim()).filter(f => f),
      dependencies: dependencies.split(',').map(d => d.trim()).filter(d => d),
      estimatedTime
    };

    this.tasks.tasks.push(newTask);
    this.saveTasks();
    console.log(`✅ Task "${title}" added successfully!`);
  }

  generateTaskId(title) {
    return title.toLowerCase()
      .replace(/[^a-z0-9\s]/g, '')
      .replace(/\s+/g, '-')
      .substring(0, 30);
  }

  async showMenu() {
    console.log('\n🚀 WebCafeShop Task Manager');
    console.log('1. View all tasks');
    console.log('2. Update task status');
    console.log('3. Add new task');
    console.log('4. Show project summary');
    console.log('5. Exit');
    
    const choice = await this.question('\nSelect an option (1-5): ');
    
    switch (choice) {
      case '1':
        this.displayTasks();
        break;
      case '2':
        await this.updateTaskStatusInteractive();
        break;
      case '3':
        await this.addNewTask();
        break;
      case '4':
        this.showProjectSummary();
        break;
      case '5':
        console.log('👋 Goodbye!');
        this.rl.close();
        return;
      default:
        console.log('❌ Invalid option!');
    }
    
    // Show menu again unless exiting
    if (choice !== '5') {
      setTimeout(() => this.showMenu(), 1000);
    }
  }

  showProjectSummary() {
    console.log('\n📊 Project Summary\n');
    console.log(`Project: ${this.tasks.project}`);
    console.log(`Current Phase: ${this.tasks.phase}`);
    console.log(`Overall Progress: ${this.tasks.progress}`);
    console.log(`Completed Tasks: ${this.tasks.completedTasks}`);
    console.log(`Remaining Tasks: ${this.tasks.totalTasks - this.tasks.completedTasks}`);
    
    const statusCounts = this.tasks.tasks.reduce((acc, task) => {
      acc[task.status] = (acc[task.status] || 0) + 1;
      return acc;
    }, {});
    
    console.log('\nTask Status Breakdown:');
    Object.entries(statusCounts).forEach(([status, count]) => {
      console.log(`  ${status.toUpperCase()}: ${count}`);
    });

    const priorityCounts = this.tasks.tasks.reduce((acc, task) => {
      acc[task.priority] = (acc[task.priority] || 0) + 1;
      return acc;
    }, {});
    
    console.log('\nPriority Breakdown:');
    Object.entries(priorityCounts).forEach(([priority, count]) => {
      console.log(`  ${priority.toUpperCase()}: ${count}`);
    });
  }

  question(prompt) {
    return new Promise((resolve) => {
      this.rl.question(prompt, resolve);
    });
  }

  async start() {
    console.log('🎉 Welcome to WebCafeShop Task Manager!');
    await this.showMenu();
  }
}

// CLI Support for automation
function handleCLIArgs() {
  const args = process.argv.slice(2);

  if (args.length === 0) {
    // No arguments, run interactive mode
    const manager = new TaskManager();
    manager.start().catch(console.error);
    return;
  }

  const manager = new TaskManager();

  // Handle automation commands
  if (args[0] === 'update-task') {
    const taskName = args[1];
    const status = args[2];
    const completedBy = args[3] || 'cli';

    if (!taskName || !status) {
      console.error('Usage: node task-manager.js update-task <taskName> <status> [completedBy]');
      process.exit(1);
    }

    const success = manager.updateTaskStatus({ taskName, status, completedBy });
    process.exit(success ? 0 : 1);
  }

  if (args[0] === 'mark-completed') {
    const taskName = args[1];
    const completedBy = args[2] || 'automation';

    if (!taskName) {
      console.error('Usage: node task-manager.js mark-completed <taskName> [completedBy]');
      process.exit(1);
    }

    const success = manager.updateTaskStatus({
      taskName,
      status: 'completed',
      completedBy
    });
    process.exit(success ? 0 : 1);
  }

  if (args[0] === 'list-tasks') {
    manager.displayTasks();
    return;
  }

  console.error('Unknown command. Available commands:');
  console.error('  update-task <taskName> <status> [completedBy]');
  console.error('  mark-completed <taskName> [completedBy]');
  console.error('  list-tasks');
  process.exit(1);
}

// Run the task manager
if (require.main === module) {
  handleCLIArgs();
}

module.exports = TaskManager;
