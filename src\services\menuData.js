export const menuCategories = [
  { id: 'all', name: 'All', icon: '☕' },
  { id: 'hot-drinks', name: 'Hot Drinks', icon: '🔥' },
  { id: 'cold-drinks', name: 'Cold Drinks', icon: '🧊' },
  { id: 'pastries', name: 'Pastries', icon: '🥐' },
  { id: 'light-meals', name: 'Light Meals', icon: '🥪' }
];

export const menuItems = [
  // Hot Drinks
  {
    id: 'espresso',
    name: 'Espresso',
    category: 'hot-drinks',
    description: 'Rich, bold espresso shot with a perfect crema',
    price: 2.50,
    image: '/images/espresso.jpg',
    available: true,
    tags: ['popular', 'strong']
  },
  {
    id: 'americano',
    name: 'Americano',
    category: 'hot-drinks',
    description: 'Espresso with hot water for a smooth, clean taste',
    price: 3.00,
    image: '/images/americano.jpg',
    available: true,
    tags: ['classic']
  },
  {
    id: 'latte',
    name: 'Latte',
    category: 'hot-drinks',
    description: 'Espresso with steamed milk and a thin layer of foam',
    price: 4.50,
    image: '/images/latte.jpg',
    available: true,
    tags: ['popular', 'creamy']
  },
  {
    id: 'cappuccino',
    name: 'Cappuccino',
    category: 'hot-drinks',
    description: 'Equal parts espresso, steamed milk, and foam',
    price: 4.00,
    image: '/images/cappuccino.jpg',
    available: true,
    tags: ['classic', 'foamy']
  },
  {
    id: 'mocha',
    name: 'Mocha',
    category: 'hot-drinks',
    description: 'Espresso with chocolate syrup and steamed milk',
    price: 5.00,
    image: '/images/mocha.jpg',
    available: true,
    tags: ['sweet', 'chocolate']
  },
  {
    id: 'hot-chocolate',
    name: 'Hot Chocolate',
    category: 'hot-drinks',
    description: 'Rich cocoa with steamed milk and whipped cream',
    price: 3.50,
    image: '/images/hot-chocolate.jpg',
    available: true,
    tags: ['sweet', 'no-coffee']
  },

  // Cold Drinks
  {
    id: 'iced-coffee',
    name: 'Iced Coffee',
    category: 'cold-drinks',
    description: 'Fresh brewed coffee served over ice',
    price: 3.25,
    image: '/images/iced-coffee.jpg',
    available: true,
    tags: ['refreshing']
  },
  {
    id: 'cold-brew',
    name: 'Cold Brew',
    category: 'cold-drinks',
    description: 'Smooth, slow-steeped coffee concentrate',
    price: 4.00,
    image: '/images/cold-brew.jpg',
    available: true,
    tags: ['popular', 'smooth']
  },
  {
    id: 'frappuccino',
    name: 'Frappuccino',
    category: 'cold-drinks',
    description: 'Blended coffee with ice and whipped cream',
    price: 5.50,
    image: '/images/frappuccino.jpg',
    available: true,
    tags: ['sweet', 'blended']
  },
  {
    id: 'iced-tea',
    name: 'Iced Tea',
    category: 'cold-drinks',
    description: 'Refreshing black tea served over ice',
    price: 2.75,
    image: '/images/iced-tea.jpg',
    available: true,
    tags: ['refreshing', 'no-coffee']
  },
  {
    id: 'smoothie',
    name: 'Berry Smoothie',
    category: 'cold-drinks',
    description: 'Mixed berries with yogurt and honey',
    price: 4.75,
    image: '/images/smoothie.jpg',
    available: true,
    tags: ['healthy', 'fruity', 'no-coffee']
  },

  // Pastries
  {
    id: 'croissant',
    name: 'Butter Croissant',
    category: 'pastries',
    description: 'Flaky, buttery French pastry',
    price: 3.25,
    image: '/images/croissant.jpg',
    available: true,
    tags: ['classic', 'buttery']
  },
  {
    id: 'chocolate-muffin',
    name: 'Chocolate Muffin',
    category: 'pastries',
    description: 'Rich chocolate muffin with chocolate chips',
    price: 2.75,
    image: '/images/chocolate-muffin.jpg',
    available: true,
    tags: ['sweet', 'chocolate']
  },
  {
    id: 'blueberry-muffin',
    name: 'Blueberry Muffin',
    category: 'pastries',
    description: 'Fresh blueberry muffin with streusel top',
    price: 2.75,
    image: '/images/blueberry-muffin.jpg',
    available: true,
    tags: ['fruity', 'popular']
  },
  {
    id: 'cookies',
    name: 'Chocolate Chip Cookies',
    category: 'pastries',
    description: 'Freshly baked chocolate chip cookies (2 pieces)',
    price: 2.50,
    image: '/images/cookies.jpg',
    available: true,
    tags: ['sweet', 'classic']
  },
  {
    id: 'cake-slice',
    name: 'Carrot Cake Slice',
    category: 'pastries',
    description: 'Moist carrot cake with cream cheese frosting',
    price: 4.25,
    image: '/images/carrot-cake.jpg',
    available: true,
    tags: ['sweet', 'special']
  },

  // Light Meals
  {
    id: 'turkey-sandwich',
    name: 'Turkey Club Sandwich',
    category: 'light-meals',
    description: 'Turkey, bacon, lettuce, tomato on sourdough',
    price: 8.50,
    image: '/images/turkey-sandwich.jpg',
    available: true,
    tags: ['protein', 'popular']
  },
  {
    id: 'veggie-sandwich',
    name: 'Veggie Sandwich',
    category: 'light-meals',
    description: 'Avocado, cucumber, sprouts, hummus on whole grain',
    price: 7.25,
    image: '/images/veggie-sandwich.jpg',
    available: true,
    tags: ['vegetarian', 'healthy']
  },
  {
    id: 'caesar-salad',
    name: 'Caesar Salad',
    category: 'light-meals',
    description: 'Romaine lettuce, parmesan, croutons, Caesar dressing',
    price: 6.75,
    image: '/images/caesar-salad.jpg',
    available: true,
    tags: ['healthy', 'classic']
  },
  {
    id: 'everything-bagel',
    name: 'Everything Bagel',
    category: 'light-meals',
    description: 'Toasted bagel with cream cheese and everything seasoning',
    price: 3.75,
    image: '/images/everything-bagel.jpg',
    available: true,
    tags: ['classic', 'savory']
  }
];

// Utility functions
export const formatPrice = (price) => `$${price.toFixed(2)}`;

export const getItemsByCategory = (category) => {
  if (category === 'all') return menuItems;
  return menuItems.filter(item => item.category === category);
};

export const searchItems = (query) => {
  if (!query.trim()) return menuItems;
  const lowercaseQuery = query.toLowerCase();
  return menuItems.filter(item => 
    item.name.toLowerCase().includes(lowercaseQuery) ||
    item.description.toLowerCase().includes(lowercaseQuery) ||
    item.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  );
};

export const getAvailableItems = () => menuItems.filter(item => item.available);

export const getItemById = (id) => menuItems.find(item => item.id === id);

export const getPopularItems = () => menuItems.filter(item => item.tags.includes('popular'));

export const getCategoryName = (categoryId) => {
  const category = menuCategories.find(cat => cat.id === categoryId);
  return category ? category.name : 'Unknown';
};

export const getPlaceholderImage = (category) => {
  const placeholders = {
    'hot-drinks': '/images/placeholder-hot-drink.jpg',
    'cold-drinks': '/images/placeholder-cold-drink.jpg',
    'pastries': '/images/placeholder-pastry.jpg',
    'light-meals': '/images/placeholder-meal.jpg'
  };
  return placeholders[category] || '/images/placeholder-default.jpg';
};