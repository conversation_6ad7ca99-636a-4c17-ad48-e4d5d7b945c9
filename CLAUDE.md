# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Common Commands

### Development
- `npm start` - Start development server on localhost:3000
- `npm test` - Run tests in watch mode
- `npm run build` - Build for production
- `npm run eject` - Eject from Create React App (one-way operation)

### Firebase Deployment
- The project uses Firebase Hosting with build output configured in firebase.json
- Hosting serves from the `build` directory with SPA routing configured

## Architecture Overview

This is a React-based web cafe shop application with Firebase authentication and hosting.

### Authentication Flow
- App.js implements authentication routing using Firebase Auth
- Protected route pattern: authenticated users see HomePage (TopFold component), unauthenticated users are redirected to Login
- Login component supports both sign-in and sign-up functionality
- Firebase configuration uses environment variables with `_REACT_APP_` prefix (note the underscore prefix)

### Component Structure
- **App.js**: Main router with authentication state management using `onAuthStateChanged`
- **Views/Login.js**: Authentication component with email/password sign-in and sign-up
- **Views/TopFold.js**: Main homepage component (imported as HomePage in App.js)
- **Views/HomePage.js**: Separate homepage component (appears unused in current routing)

### Firebase Setup
- Firebase config in src/firebase.js exports `auth` for authentication
- Uses Firebase v9+ modular SDK
- Environment variables required for Firebase configuration (API keys, project ID, etc.)
- Analytics is initialized but not actively used in components

### State Management
- Uses React hooks for local state management
- No external state management library (Redux, Zustand, etc.)
- Authentication state managed in App.js and passed down through routing

### Styling
- Tailwind CSS for utility-first styling
- Component-specific CSS files for custom styles
- Responsive design with mobile-first approach

### Current Features
- User authentication (sign in/sign up)
- Protected routing
- Firebase integration
- Responsive hero section
- Modern glassmorphic design elements

### Development Guidelines
- Follow React functional component patterns
- Use Firebase v9+ modular SDK
- Maintain consistent Tailwind CSS utility classes
- Implement proper error handling and loading states
- Follow accessibility best practices