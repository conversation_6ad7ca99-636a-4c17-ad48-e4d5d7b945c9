import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import TopFold from './TopFold';
import { renderWithRouter, waitForAnimation, hasAnimationClass, mockIntersectionObserver } from '../utils/testUtils';

// Mock MenuSection component to isolate TopFold testing
jest.mock('../components/MenuSection', () => {
  return function MockMenuSection() {
    return <div data-testid="menu-section">Menu Section</div>;
  };
});

// Mock the logo import
jest.mock('../assets/images/logo512.png', () => 'test-logo.png');

describe('TopFold Component', () => {
  let user;

  beforeEach(() => {
    user = userEvent.setup();
    jest.clearAllMocks();
  });

  describe('Rendering Tests', () => {
    test('renders without crashing', () => {
      render(<TopFold />);
      expect(screen.getByText(/Holding/)).toBeInTheDocument();
    });

    test('renders all main headings', () => {
      render(<TopFold />);
      
      expect(screen.getByText(/Holding/)).toBeInTheDocument();
      expect(screen.getByText(/Company\./)).toBeInTheDocument();
      expect(screen.getAllByText(/AI/)).toHaveLength(2);
      expect(screen.getByText(/Investor\./)).toBeInTheDocument();
      expect(screen.getByText(/WebCafeShop/)).toBeInTheDocument();
    });

    test('renders logo with correct alt text', () => {
      render(<TopFold />);
      
      const logo = screen.getByAltText('Webcafe Logo');
      expect(logo).toBeInTheDocument();
      expect(logo).toHaveAttribute('src', 'test-logo.png');
    });

    test('renders call-to-action buttons', () => {
      render(<TopFold />);
      
      expect(screen.getByRole('button', { name: /get started/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /try now/i })).toBeInTheDocument();
    });

    test('renders scroll indicator', () => {
      render(<TopFold />);
      
      expect(screen.getByText(/scroll to explore/i)).toBeInTheDocument();
    });
  });

  describe('Animation Classes Tests', () => {
    test('text content has fade-in animation class', () => {
      render(<TopFold />);
      
      const textContent = screen.getByText(/Holding/).closest('.animate-fade-in');
      expect(textContent).toBeInTheDocument();
      expect(textContent).toHaveClass('animate-fade-in');
    });

    test('logo has bounce-gentle animation class', () => {
      render(<TopFold />);
      
      const logo = screen.getByAltText('Webcafe Logo');
      expect(logo).toHaveClass('animate-bounce-gentle');
    });

    test('scroll indicator has bounce animation class', () => {
      render(<TopFold />);
      
      const scrollIndicator = screen.getByText(/scroll to explore/i).closest('.animate-bounce');
      expect(scrollIndicator).toBeInTheDocument();
    });

    test('decorative rings have pulse animation', () => {
      render(<TopFold />);
      
      const pulseElements = document.querySelectorAll('.animate-pulse');
      expect(pulseElements.length).toBeGreaterThan(0);
    });

    test('gradient text has bounce-gentle animation', () => {
      render(<TopFold />);
      
      const gradientText = screen.getByText(/Company\./);
      expect(gradientText).toHaveClass('animate-bounce-gentle');
    });
  });

  describe('Styling Tests', () => {
    test('applies glassmorphic background styles', () => {
      render(<TopFold />);
      
      const section = document.querySelector('section');
      expect(section).toHaveClass('bg-gradient-to-br', 'from-white', 'via-primary-50', 'to-secondary-50');
    });

    test('text content has proper performance optimization', () => {
      render(<TopFold />);
      
      const textContent = screen.getByText(/Holding/).closest('.animate-fade-in');
      expect(textContent).toHaveStyle({ willChange: 'opacity, transform' });
    });

    test('buttons have proper hover styles', () => {
      render(<TopFold />);
      
      const getStartedBtn = screen.getByRole('button', { name: /get started/i });
      expect(getStartedBtn).toHaveClass('hover:scale-105', 'transform', 'transition-all', 'duration-300');
      
      const tryNowBtn = screen.getByRole('button', { name: /try now/i });
      expect(tryNowBtn).toHaveClass('hover:scale-105', 'transform', 'transition-all', 'duration-300');
    });

    test('logo container has proper shadow and styling', () => {
      render(<TopFold />);
      
      const logoContainer = screen.getByAltText('Webcafe Logo').closest('.bg-white');
      expect(logoContainer).toHaveClass('rounded-full', 'shadow-strong');
    });
  });

  describe('Interactive Elements Tests', () => {
    test('Get Started button scrolls to features section', async () => {
      // Mock getElementById to return a mock element
      const mockElement = { scrollIntoView: jest.fn() };
      jest.spyOn(document, 'getElementById').mockReturnValue(mockElement);
      
      render(<TopFold />);
      
      const getStartedBtn = screen.getByRole('button', { name: /get started/i });
      await user.click(getStartedBtn);
      
      expect(document.getElementById).toHaveBeenCalledWith('features');
      expect(mockElement.scrollIntoView).toHaveBeenCalledWith({
        behavior: 'smooth'
      });
      
      document.getElementById.mockRestore();
    });

    test('Try Now button has correct href', () => {
      render(<TopFold />);
      
      const tryNowLink = screen.getByRole('button', { name: /try now/i }).closest('a');
      expect(tryNowLink).toHaveAttribute('href', '#features');
    });

    test('buttons have proper focus states', () => {
      render(<TopFold />);
      
      const getStartedBtn = screen.getByRole('button', { name: /get started/i });
      expect(getStartedBtn).toHaveClass('focus:outline-none', 'focus:ring-2', 'focus:ring-primary-500');
      
      const tryNowBtn = screen.getByRole('button', { name: /try now/i });
      expect(tryNowBtn).toHaveClass('focus:outline-none', 'focus:ring-2', 'focus:ring-primary-500');
    });
  });

  describe('Accessibility Tests', () => {
    test('has proper heading hierarchy', () => {
      render(<TopFold />);
      
      const h1Elements = screen.getAllByRole('heading', { level: 1 });
      expect(h1Elements).toHaveLength(3);
    });

    test('buttons have proper labels', () => {
      render(<TopFold />);
      
      expect(screen.getByRole('button', { name: /get started/i })).toBeInTheDocument();
      expect(screen.getByRole('button', { name: /try now/i })).toBeInTheDocument();
    });

    test('logo has proper alt text', () => {
      render(<TopFold />);
      
      const logo = screen.getByAltText('Webcafe Logo');
      expect(logo).toBeInTheDocument();
    });

    test('has proper section structure', () => {
      render(<TopFold />);
      
      const section = document.querySelector('section');
      expect(section).toBeInTheDocument();
    });
  });

  describe('Responsive Design Tests', () => {
    test('uses responsive grid classes', () => {
      render(<TopFold />);
      
      const gridContainer = document.querySelector('.grid-cols-1.lg\\:grid-cols-2');
      expect(gridContainer).toBeInTheDocument();
    });

    test('has responsive text sizing', () => {
      render(<TopFold />);
      
      const mainHeading = screen.getByText(/Holding/);
      expect(mainHeading).toHaveClass('text-4xl', 'sm:text-5xl', 'lg:text-6xl', 'xl:text-7xl');
    });

    test('has responsive padding and margins', () => {
      render(<TopFold />);
      
      const container = document.querySelector('.px-4.sm\\:px-6.lg\\:px-8');
      expect(container).toBeInTheDocument();
    });

    test('logo has responsive sizing', () => {
      render(<TopFold />);
      
      const logo = screen.getByAltText('Webcafe Logo');
      expect(logo).toHaveClass('w-64', 'h-64', 'sm:w-80', 'sm:h-80', 'lg:w-96', 'lg:h-96');
    });
  });

  describe('Performance Tests', () => {
    test('uses willChange for performance optimization', () => {
      render(<TopFold />);
      
      const animatedElement = screen.getByText(/Holding/).closest('.animate-fade-in');
      expect(animatedElement).toHaveStyle({ willChange: 'opacity, transform' });
    });

    test('logo has proper loading optimization', () => {
      render(<TopFold />);
      
      const logo = screen.getByAltText('Webcafe Logo');
      expect(logo).toHaveClass('object-contain');
    });
  });

  describe('Animation Timing Tests', () => {
    test('buttons have proper transition duration', () => {
      render(<TopFold />);
      
      const getStartedBtn = screen.getByRole('button', { name: /get started/i });
      expect(getStartedBtn).toHaveClass('duration-300');
      
      const tryNowBtn = screen.getByRole('button', { name: /try now/i });
      expect(tryNowBtn).toHaveClass('duration-300');
    });

    test('hover effects have proper timing', () => {
      render(<TopFold />);
      
      const getStartedBtn = screen.getByRole('button', { name: /get started/i });
      const hoverOverlay = getStartedBtn.querySelector('.group-hover\\:opacity-100');
      expect(hoverOverlay).toHaveClass('duration-300');
    });
  });

  describe('Integration Tests', () => {
    test('renders MenuSection component', () => {
      render(<TopFold />);
      
      expect(screen.getByTestId('menu-section')).toBeInTheDocument();
    });

    test('has proper section ID for navigation', () => {
      render(<TopFold />);
      
      // The MenuSection should have the features id
      const menuSection = screen.getByTestId('menu-section');
      expect(menuSection).toBeInTheDocument();
    });
  });

  describe('Error Handling Tests', () => {
    test('handles missing features element gracefully', async () => {
      render(<TopFold />);
      
      // Mock getElementById to return null
      const originalGetElementById = document.getElementById;
      document.getElementById = jest.fn().mockReturnValue(null);
      
      const getStartedBtn = screen.getByRole('button', { name: /get started/i });
      await user.click(getStartedBtn);
      
      // Should not throw an error
      expect(document.getElementById).toHaveBeenCalledWith('features');
      
      // Restore original function
      document.getElementById = originalGetElementById;
    });
  });
});