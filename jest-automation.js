#!/usr/bin/env node

/**
 * Jest Test Automation Integration for WebCafeShop
 * Hooks into Jest test lifecycle to trigger task completion on successful tests
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class JestTaskAutomation {
  constructor() {
    this.testResults = {
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      testSuites: [],
      startTime: null,
      endTime: null
    };
  }

  /**
   * Jest globalSetup hook - called before all tests
   */
  static async globalSetup() {
    console.log('🧪 Jest Test Automation: Starting test suite...');
    const automation = new JestTaskAutomation();
    automation.testResults.startTime = new Date().toISOString();
    
    // Save initial state
    automation.saveTestState();
  }

  /**
   * Jest globalTeardown hook - called after all tests complete
   */
  static async globalTeardown() {
    console.log('🧪 Jest Test Automation: Test suite completed');
    const automation = new JestTaskAutomation();
    automation.testResults.endTime = new Date().toISOString();
    
    // Load test results and trigger task completion if tests passed
    automation.loadTestState();
    automation.processTestResults();
  }

  /**
   * Custom test reporter to capture test results
   */
  static testReporter(testResults) {
    const automation = new JestTaskAutomation();
    
    automation.testResults.totalTests = testResults.numTotalTests;
    automation.testResults.passedTests = testResults.numPassedTests;
    automation.testResults.failedTests = testResults.numFailedTests;
    
    // Process each test suite
    testResults.testResults.forEach(suiteResult => {
      const suite = {
        testFilePath: suiteResult.testFilePath,
        numPassingTests: suiteResult.numPassingTests,
        numFailingTests: suiteResult.numFailingTests,
        testResults: suiteResult.testResults.map(test => ({
          title: test.title,
          status: test.status,
          duration: test.duration
        }))
      };
      
      automation.testResults.testSuites.push(suite);
    });

    automation.saveTestState();
    
    // If all tests passed, trigger task completion
    if (testResults.numFailedTests === 0 && testResults.numPassedTests > 0) {
      automation.triggerTaskCompletion();
    }
  }

  saveTestState() {
    try {
      fs.writeFileSync(
        path.join(process.cwd(), '.jest-automation-state.json'),
        JSON.stringify(this.testResults, null, 2)
      );
    } catch (error) {
      console.error('❌ Failed to save test state:', error.message);
    }
  }

  loadTestState() {
    try {
      const statePath = path.join(process.cwd(), '.jest-automation-state.json');
      if (fs.existsSync(statePath)) {
        const data = fs.readFileSync(statePath, 'utf8');
        this.testResults = JSON.parse(data);
      }
    } catch (error) {
      console.error('❌ Failed to load test state:', error.message);
    }
  }

  processTestResults() {
    const { totalTests, passedTests, failedTests } = this.testResults;
    
    console.log(`\n📊 Test Results Summary:`);
    console.log(`   Total Tests: ${totalTests}`);
    console.log(`   Passed: ${passedTests}`);
    console.log(`   Failed: ${failedTests}`);
    
    if (failedTests === 0 && passedTests > 0) {
      console.log('✅ All tests passed! Triggering task completion...');
      this.triggerTaskCompletion();
    } else if (failedTests > 0) {
      console.log('❌ Some tests failed. No task completion triggered.');
    } else {
      console.log('⚠️ No tests were run.');
    }
  }

  triggerTaskCompletion() {
    try {
      // Mark testing-related tasks as completed
      const tasksToComplete = [
        'unit-testing',
        'testing-optimization',
        'performance-testing'
      ];

      tasksToComplete.forEach(taskName => {
        try {
          execSync(
            `node task-manager.js mark-completed "${taskName}" "jest-automation"`,
            { stdio: 'inherit' }
          );
          console.log(`✅ Marked task as completed: ${taskName}`);
        } catch (error) {
          console.log(`ℹ️ Task not found or already completed: ${taskName}`);
        }
      });

      // Also try to mark any task that matches test patterns
      this.markTestRelatedTasks();
      
    } catch (error) {
      console.error('❌ Error triggering task completion:', error.message);
    }
  }

  markTestRelatedTasks() {
    try {
      // Load current tasks to find test-related ones
      const taskFile = path.join(process.cwd(), 'augment.taskplan.json');
      if (fs.existsSync(taskFile)) {
        const taskData = JSON.parse(fs.readFileSync(taskFile, 'utf8'));
        
        taskData.tasks.forEach(task => {
          const taskTitle = task.title.toLowerCase();
          const taskDesc = task.description.toLowerCase();
          
          // Check if task is test-related and not already completed
          if (task.status !== 'completed' && 
              (taskTitle.includes('test') || 
               taskTitle.includes('testing') ||
               taskDesc.includes('test') ||
               taskDesc.includes('jest'))) {
            
            try {
              execSync(
                `node task-manager.js mark-completed "${task.id}" "jest-automation"`,
                { stdio: 'inherit' }
              );
              console.log(`✅ Marked test-related task as completed: ${task.title}`);
            } catch (error) {
              console.log(`ℹ️ Could not mark task as completed: ${task.title}`);
            }
          }
        });
      }
    } catch (error) {
      console.error('❌ Error processing test-related tasks:', error.message);
    }
  }

  /**
   * Create a custom Jest reporter class
   */
  static createReporter() {
    return class TaskAutomationReporter {
      onRunComplete(contexts, results) {
        JestTaskAutomation.testReporter(results);
      }
    };
  }

  /**
   * Setup Jest configuration for automation
   */
  static setupJestConfig() {
    const jestConfig = {
      globalSetup: '<rootDir>/jest-automation.js',
      globalTeardown: '<rootDir>/jest-automation.js',
      reporters: [
        'default',
        ['<rootDir>/jest-automation.js', {}]
      ]
    };

    console.log('📝 Jest configuration for task automation:');
    console.log(JSON.stringify(jestConfig, null, 2));
    
    return jestConfig;
  }
}

// Export for Jest configuration
module.exports = JestTaskAutomation;

// CLI support
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args[0] === 'setup') {
    console.log('🔧 Setting up Jest automation...');
    JestTaskAutomation.setupJestConfig();
  } else if (args[0] === 'test-trigger') {
    console.log('🧪 Testing task completion trigger...');
    const automation = new JestTaskAutomation();
    automation.testResults.passedTests = 5;
    automation.testResults.failedTests = 0;
    automation.triggerTaskCompletion();
  } else if (args[0] === 'global-setup') {
    JestTaskAutomation.globalSetup();
  } else if (args[0] === 'global-teardown') {
    JestTaskAutomation.globalTeardown();
  } else {
    console.log('Usage:');
    console.log('  node jest-automation.js setup           # Show Jest config');
    console.log('  node jest-automation.js test-trigger    # Test task completion');
    console.log('  node jest-automation.js global-setup    # Jest global setup');
    console.log('  node jest-automation.js global-teardown # Jest global teardown');
  }
}
