import express from 'express';
import cors from 'cors';
import fs from 'fs';

const app = express();
const port = 3000;

app.use(cors());
app.use(express.json());

let isEnabled = false;
let config = {};

// Load configuration from .codes.json
try {
  const configData = fs.readFileSync('/usr/src/app/.codes.json', 'utf8');
  config = JSON.parse(configData);
  console.log('Loaded configuration:', config);
} catch (error) {
  console.warn('Could not load .codes.json:', error.message);
  config = { project: 'default', crawlDepth: 2 };
}

// Health check endpoint
app.get('/health', (req, res) => {
  res.status(isEnabled ? 200 : 503).json({
    status: isEnabled ? 'enabled' : 'disabled',
    server: 'firecrawl-mcp',
    config: config
  });
});

// Enable Firecrawl MCP Server
app.post('/enable', (req, res) => {
  const apiKey = process.env.FIRECRAWL_API_KEY;
  
  if (!apiKey || apiKey === 'your_firecrawl_api_key_here') {
    return res.status(400).json({
      error: 'FIRECRAWL_API_KEY not configured. Please set your API key in .env file.'
    });
  }
  
  isEnabled = true;
  console.log('Firecrawl MCP Server enabled');
  
  res.json({
    message: 'Firecrawl MCP Server enabled successfully',
    config: config,
    hasApiKey: !!apiKey
  });
});

// Disable Firecrawl MCP Server
app.post('/disable', (req, res) => {
  isEnabled = false;
  console.log('Firecrawl MCP Server disabled');
  
  res.json({
    message: 'Firecrawl MCP Server disabled'
  });
});

// Get server status
app.get('/status', (req, res) => {
  res.json({
    enabled: isEnabled,
    server: 'firecrawl-mcp',
    config: config,
    hasApiKey: !!(process.env.FIRECRAWL_API_KEY && process.env.FIRECRAWL_API_KEY !== 'your_firecrawl_api_key_here')
  });
});

// Mock crawl endpoint for testing
app.post('/crawl', async (req, res) => {
  if (!isEnabled) {
    return res.status(503).json({ error: 'Firecrawl MCP Server is not enabled' });
  }
  
  const { url } = req.body;
  
  if (!url) {
    return res.status(400).json({ error: 'URL is required' });
  }
  
  // Mock response - in a real implementation, this would use Firecrawl API
  res.json({
    success: true,
    url: url,
    data: {
      title: `Mock crawl of ${url}`,
      content: 'This is a mock response. Configure FIRECRAWL_API_KEY for real crawling.',
      metadata: {
        crawlDepth: config.crawlDepth || 2,
        project: config.project || 'unknown'
      }
    }
  });
});

app.listen(port, '0.0.0.0', () => {
  console.log(`Firecrawl MCP Server running on port ${port}`);
  console.log(`Configuration: ${JSON.stringify(config, null, 2)}`);
  console.log(`API Key configured: ${!!(process.env.FIRECRAWL_API_KEY && process.env.FIRECRAWL_API_KEY !== 'your_firecrawl_api_key_here')}`);
});