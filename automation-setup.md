# WebCafeShop Task Automation Setup

## Overview

This document describes the automated task completion system that integrates with GitHub PR merges and Jest test success to automatically mark tasks as completed in the task management system.

## Components

### 1. Enhanced Task Manager (`task-manager.js`)

**New Functions:**
- `updateTaskStatus({ taskName, status, completedBy })` - Programmatically update task status
- `updateAugmentMemory(taskId, status)` - Inject completion metadata into task objects

**CLI Commands:**
```bash
# Update task status
node task-manager.js update-task <taskName> <status> [completedBy]

# Mark task as completed
node task-manager.js mark-completed <taskName> [completedBy]

# List all tasks
node task-manager.js list-tasks
```

### 2. GitHub Webhook Handler (`webhook-handler.js`)

**Features:**
- Listens for GitHub webhook events (PR merges, pushes, workflow runs)
- Automatically extracts task information from PR titles and branch names
- Marks tasks as completed when PRs are merged to main
- Supports manual testing endpoints

**Usage:**
```bash
# Start webhook server
node webhook-handler.js start

# Test task completion trigger
node webhook-handler.js test-trigger <taskName>
```

**Webhook Endpoint:** `http://localhost:3001/webhook`
**Health Check:** `http://localhost:3001/health`

### 3. Jest Test Integration (`jest-automation.js`)

**Features:**
- Hooks into Jest test lifecycle
- Automatically marks testing-related tasks as completed when all tests pass
- Captures test results and metadata
- Supports both automated and manual test triggers

**Usage:**
```bash
# Test the automation trigger
node jest-automation.js test-trigger

# Show Jest configuration
node jest-automation.js setup
```

## Setup Instructions

### 1. Install Dependencies

```bash
npm install express --save-dev
```

### 2. Configure GitHub Webhook

1. Go to your GitHub repository settings
2. Navigate to "Webhooks"
3. Add a new webhook with:
   - **Payload URL:** `https://your-domain.com/webhook` (or use ngrok for local testing)
   - **Content type:** `application/json`
   - **Secret:** Set `GITHUB_WEBHOOK_SECRET` environment variable
   - **Events:** Select "Pull requests", "Pushes", and "Workflow runs"

### 3. Environment Variables

Create a `.env` file:
```env
GITHUB_WEBHOOK_SECRET=your-webhook-secret-here
WEBHOOK_PORT=3001
```

### 4. NPM Scripts

The following scripts are available in `package.json`:

```bash
# Run tests with automation
npm run test:automation

# Run tests in CI mode with automation
npm run test:ci

# Start task manager interactively
npm run task-manager

# Start webhook server
npm run webhook-server
```

## Automation Triggers

### GitHub PR Merge Triggers

Tasks are automatically marked as completed when:

1. **PR is merged to main branch**
2. **PR title or branch name contains task patterns:**
   - `task: <task-name>`
   - `fix: <task-name>`
   - `feat: <task-name>`
   - `implement: <task-name>`

**Example PR titles that trigger automation:**
- "feat: responsive-design - Add mobile-first styling"
- "task: shopping-cart-system - Implement cart functionality"
- "fix: unit-testing - Resolve test failures"

### Jest Test Success Triggers

Tasks are automatically marked as completed when:

1. **All tests pass** (no failed tests)
2. **Task names match testing patterns:**
   - Contains "test" or "testing"
   - Task ID matches predefined testing tasks
   - Task description mentions "jest" or "test"

### Commit Message Triggers

Tasks can be marked as completed via commit messages:
- "completed task: <task-name>"
- "finished <task-name>"
- "done: <task-name>"

## Task Naming Conventions

For automation to work effectively, use consistent task naming:

- **Use kebab-case:** `responsive-design`, `shopping-cart-system`
- **Match task IDs:** Ensure PR branches and titles reference actual task IDs
- **Be descriptive:** Include key terms that automation can recognize

## Testing the Automation

### 1. Test Task Manager CLI

```bash
# List current tasks
node task-manager.js list-tasks

# Mark a task as completed
node task-manager.js mark-completed "responsive-styling" "manual-test"

# Update task status
node task-manager.js update-task "unit-testing" "in-progress" "developer"
```

### 2. Test Webhook Handler

```bash
# Start the webhook server
node webhook-handler.js start

# In another terminal, test task completion
curl -X POST http://localhost:3001/trigger-task-completion \
  -H "Content-Type: application/json" \
  -d '{"taskName": "responsive-styling", "completedBy": "webhook-test"}'
```

### 3. Test Jest Integration

```bash
# Test the Jest automation trigger
node jest-automation.js test-trigger

# Run tests with automation
npm run test:automation
```

## Monitoring and Logs

### Task Manager Logs
- ✅ Task completion confirmations
- ❌ Error messages for failed updates
- 🧠 Augment memory updates

### Webhook Handler Logs
- 📨 Incoming webhook events
- 🎉 PR merge notifications
- 🔄 Task completion triggers

### Jest Integration Logs
- 🧪 Test suite start/completion
- 📊 Test results summary
- ✅ Automated task completions

## Troubleshooting

### Common Issues

1. **Task not found errors**
   - Verify task ID matches exactly
   - Check task naming conventions
   - Use `list-tasks` to see available tasks

2. **Webhook not triggering**
   - Verify webhook URL is accessible
   - Check GitHub webhook secret configuration
   - Review webhook delivery logs in GitHub

3. **Jest automation not working**
   - Ensure all tests are passing
   - Check test result processor configuration
   - Verify task names match testing patterns

### Debug Commands

```bash
# Check webhook server health
curl http://localhost:3001/health

# View current task status
node task-manager.js list-tasks

# Test webhook manually
node webhook-handler.js test-trigger "test-task"

# Test Jest automation
node jest-automation.js test-trigger
```

## Security Considerations

1. **Webhook Secret:** Always use a strong webhook secret
2. **Port Security:** Consider firewall rules for webhook port
3. **Input Validation:** Task names are validated before processing
4. **Error Handling:** Failed automations don't crash the system

## Future Enhancements

- [ ] Slack/Discord notifications for task completions
- [ ] Integration with project management tools (Jira, Trello)
- [ ] Advanced task dependency management
- [ ] Automated progress reporting
- [ ] Integration with CI/CD pipelines

---

**Last Updated:** 2025-07-06  
**Branch:** `codex/automate-task-feedback`
