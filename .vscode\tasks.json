{"version": "2.0.0", "tasks": [{"label": "Launch Claude Code", "type": "shell", "command": "wsl", "args": ["-e", "bash", "-c", "source ~/.bashrc && cd /mnt/c/Users/<USER>/OneDrive/Documents/GitHub/webcafeshop && claude"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": [], "detail": "Launch Claude Code CLI in WSL within the current workspace"}, {"label": "<PERSON> - New Session", "type": "shell", "command": "wsl", "args": ["-e", "bash", "-c", "source ~/.bashrc && cd /mnt/c/Users/<USER>/OneDrive/Documents/GitHub/webcafeshop && claude --new"], "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": true, "panel": "new", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": [], "detail": "Launch Claude Code CLI with a new session in WSL"}]}