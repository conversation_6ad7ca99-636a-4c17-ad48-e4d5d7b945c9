/**
 * Initializes Firebase using environment based configuration.
 */
// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

/**
 * Firebase configuration loaded from environment variables.
 * Create a `.env.local` file with the `REACT_APP_*` keys when developing
 * locally so credentials remain out of source control.
 */
const firebaseConfig = {
<<<<<<< HEAD
  // API key used to authenticate requests from your app
  apiKey: process.env.REACT_APP_API_KEY,
  // Auth domain for Firebase Authentication
  authDomain: process.env.REACT_APP_AUTH_DOMAIN,
  // Unique identifier for your Firebase project
  projectId: process.env.REACT_APP_PROJECT_ID,
  // Cloud Storage bucket name
  storageBucket: process.env.REACT_APP_STORAGE_BUCKET,
  // Sender ID for Firebase Cloud Messaging
  messagingSenderId: process.env.REACT_APP_MESSAGING_SENDER_ID,
  // Unique identifier for the Firebase app
  appId: process.env.REACT_APP_APP_ID,
  // ID used for Google Analytics and other measurement features
  measurementId: process.env.REACT_APP_MEASUREMENT_ID,
=======
  apiKey: process.env.REACT_APP_API_KEY,
  authDomain: process.env.REACT_APP_AUTH_DOMAIN,
  projectId: process.env.REACT_APP_PROJECT_ID,
  storageBucket: process.env.REACT_APP_STORAGE_BUCKET,
  messagingSenderId: process.env.REACT_APP_MESSAGING_SENDER_ID,
  appId: process.env.REACT_APP_APP_ID,
  measurementId: process.env.REACT_APP_MEASUREMENT_ID
>>>>>>> 86c8666 (WIP: commit all local changes before pulling latest main)
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);
