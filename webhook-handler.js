#!/usr/bin/env node

/**
 * GitHub Webhook Handler for WebCafeShop Task Automation
 * Listens for PR merge events and automatically marks tasks as completed
 */

const express = require('express');
const crypto = require('crypto');
const { execSync } = require('child_process');
const TaskManager = require('./task-manager');

class GitHubWebhookHandler {
  constructor(options = {}) {
    this.port = options.port || process.env.WEBHOOK_PORT || 3001;
    this.secret = options.secret || process.env.GITHUB_WEBHOOK_SECRET;
    this.app = express();
    this.taskManager = new TaskManager();
    
    this.setupMiddleware();
    this.setupRoutes();
  }

  setupMiddleware() {
    // Parse JSON payloads
    this.app.use('/webhook', express.raw({ type: 'application/json' }));
    
    // Logging middleware
    this.app.use((req, res, next) => {
      console.log(`${new Date().toISOString()} - ${req.method} ${req.path}`);
      next();
    });
  }

  setupRoutes() {
    // Health check endpoint
    this.app.get('/health', (req, res) => {
      res.json({ 
        status: 'healthy', 
        timestamp: new Date().toISOString(),
        service: 'webhook-handler'
      });
    });

    // GitHub webhook endpoint
    this.app.post('/webhook', (req, res) => {
      try {
        // Verify GitHub signature if secret is configured
        if (this.secret && !this.verifySignature(req.body, req.headers['x-hub-signature-256'])) {
          console.error('❌ Invalid webhook signature');
          return res.status(401).json({ error: 'Invalid signature' });
        }

        const payload = JSON.parse(req.body.toString());
        const event = req.headers['x-github-event'];

        console.log(`📨 Received GitHub event: ${event}`);
        
        this.handleGitHubEvent(event, payload);
        
        res.status(200).json({ message: 'Webhook processed successfully' });
      } catch (error) {
        console.error('❌ Error processing webhook:', error.message);
        res.status(500).json({ error: 'Internal server error' });
      }
    });

    // Manual trigger endpoint for testing
    this.app.post('/trigger-task-completion', express.json(), (req, res) => {
      const { taskName, completedBy = 'manual-trigger' } = req.body;
      
      if (!taskName) {
        return res.status(400).json({ error: 'taskName is required' });
      }

      const success = this.taskManager.updateTaskStatus({
        taskName,
        status: 'completed',
        completedBy
      });

      res.json({ success, taskName, completedBy });
    });
  }

  verifySignature(payload, signature) {
    if (!signature) return false;
    
    const expectedSignature = 'sha256=' + crypto
      .createHmac('sha256', this.secret)
      .update(payload)
      .digest('hex');
    
    return crypto.timingSafeEqual(
      Buffer.from(signature),
      Buffer.from(expectedSignature)
    );
  }

  handleGitHubEvent(event, payload) {
    switch (event) {
      case 'pull_request':
        this.handlePullRequestEvent(payload);
        break;
      case 'push':
        this.handlePushEvent(payload);
        break;
      case 'workflow_run':
        this.handleWorkflowRunEvent(payload);
        break;
      default:
        console.log(`ℹ️ Unhandled event type: ${event}`);
    }
  }

  handlePullRequestEvent(payload) {
    const { action, pull_request } = payload;
    
    if (action === 'closed' && pull_request.merged) {
      console.log(`🎉 PR merged: ${pull_request.title}`);
      
      // Extract task information from PR title or branch name
      const taskInfo = this.extractTaskFromPR(pull_request);
      
      if (taskInfo) {
        console.log(`🔄 Marking task as completed: ${taskInfo.taskName}`);
        
        this.taskManager.updateTaskStatus({
          taskName: taskInfo.taskName,
          status: 'completed',
          completedBy: `github-pr-${pull_request.number}`
        });

        // Optional: Add comment to PR about task completion
        this.addPRComment(pull_request, taskInfo);
      }
    }
  }

  handlePushEvent(payload) {
    const { ref, commits } = payload;
    
    // Only handle pushes to main branch
    if (ref === 'refs/heads/main') {
      console.log(`📦 Push to main branch with ${commits.length} commits`);
      
      // Check commit messages for task completion patterns
      commits.forEach(commit => {
        const taskInfo = this.extractTaskFromCommit(commit);
        if (taskInfo) {
          console.log(`🔄 Marking task as completed from commit: ${taskInfo.taskName}`);
          
          this.taskManager.updateTaskStatus({
            taskName: taskInfo.taskName,
            status: 'completed',
            completedBy: `github-commit-${commit.id.substring(0, 7)}`
          });
        }
      });
    }
  }

  handleWorkflowRunEvent(payload) {
    const { action, workflow_run } = payload;
    
    if (action === 'completed' && workflow_run.conclusion === 'success') {
      console.log(`✅ Workflow completed successfully: ${workflow_run.name}`);
      
      // If it's a test workflow, mark testing tasks as completed
      if (workflow_run.name.toLowerCase().includes('test')) {
        this.taskManager.updateTaskStatus({
          taskName: 'unit-testing',
          status: 'completed',
          completedBy: `github-workflow-${workflow_run.id}`
        });
      }
    }
  }

  extractTaskFromPR(pullRequest) {
    const title = pullRequest.title.toLowerCase();
    const branchName = pullRequest.head.ref.toLowerCase();
    
    // Look for task patterns in PR title or branch name
    const patterns = [
      /task[:\-\s]*([a-z0-9\-]+)/i,
      /fix[:\-\s]*([a-z0-9\-]+)/i,
      /feat[:\-\s]*([a-z0-9\-]+)/i,
      /implement[:\-\s]*([a-z0-9\-]+)/i
    ];

    for (const pattern of patterns) {
      const match = title.match(pattern) || branchName.match(pattern);
      if (match) {
        return { taskName: match[1] };
      }
    }

    return null;
  }

  extractTaskFromCommit(commit) {
    const message = commit.message.toLowerCase();
    
    // Look for completion patterns in commit messages
    const patterns = [
      /complete[sd]?\s+task[:\-\s]*([a-z0-9\-]+)/i,
      /finish[ed]?\s+([a-z0-9\-]+)/i,
      /done[:\-\s]*([a-z0-9\-]+)/i
    ];

    for (const pattern of patterns) {
      const match = message.match(pattern);
      if (match) {
        return { taskName: match[1] };
      }
    }

    return null;
  }

  addPRComment(pullRequest, taskInfo) {
    // This would require GitHub API integration
    console.log(`💬 Would add comment to PR #${pullRequest.number} about task completion: ${taskInfo.taskName}`);
  }

  start() {
    this.app.listen(this.port, () => {
      console.log(`🚀 GitHub Webhook Handler running on port ${this.port}`);
      console.log(`📡 Webhook endpoint: http://localhost:${this.port}/webhook`);
      console.log(`🏥 Health check: http://localhost:${this.port}/health`);
    });
  }

  // Static method for CLI usage
  static startServer(options = {}) {
    const handler = new GitHubWebhookHandler(options);
    handler.start();
    return handler;
  }
}

// CLI support
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args[0] === 'start') {
    GitHubWebhookHandler.startServer();
  } else if (args[0] === 'test-trigger') {
    const taskName = args[1];
    if (!taskName) {
      console.error('Usage: node webhook-handler.js test-trigger <taskName>');
      process.exit(1);
    }
    
    const taskManager = new TaskManager();
    const success = taskManager.updateTaskStatus({
      taskName,
      status: 'completed',
      completedBy: 'test-trigger'
    });
    
    console.log(success ? '✅ Task marked as completed' : '❌ Failed to update task');
    process.exit(success ? 0 : 1);
  } else {
    console.log('Usage:');
    console.log('  node webhook-handler.js start           # Start webhook server');
    console.log('  node webhook-handler.js test-trigger <taskName>  # Test task completion');
  }
}

module.exports = GitHubWebhookHandler;
