import React from 'react';
import {formatPrice, getPlaceholderImage} from '../services/menuData';

const MenuItem = ({item}) => {
  const handleImageError = (e) => {
    e.target.src = getPlaceholderImage(item.category);
  };

  return (
      <div
        className="group bg-white/70 backdrop-blur-md rounded-xl p-6 border border-white/20 shadow-lg hover:shadow-xl transform hover:-translate-y-1 transition-all duration-300 hover:bg-white/80"
        style={{ willChange: 'opacity, transform' }}
      >
      <div className="relative overflow-hidden rounded-lg mb-4">
        <img
          src={item.image}
          alt={item.name}
          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
          onError={handleImageError}
        />
        {!item.available && (
          <div className="absolute inset-0 bg-black/50 flex items-center justify-center rounded-lg">
            <span className="text-white font-medium bg-red-500/90 px-3 py-1 rounded-full text-sm">
              Unavailable
            </span>
          </div>
        )}
        {item.tags.includes('popular') && (
          <div className="absolute top-2 right-2 bg-gradient-to-r from-amber-400 to-orange-500 text-white text-xs font-bold px-2 py-1 rounded-full">
            Popular
          </div>
        )}
      </div>
      
      <div className="space-y-2">
        <div className="flex justify-between items-start">
          <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary-600 transition-colors">
            {item.name}
          </h3>
          <span className="text-lg font-bold text-primary-600 bg-primary-50 px-2 py-1 rounded-lg">
            {formatPrice(item.price)}
          </span>
        </div>
        
        <p className="text-gray-600 text-sm leading-relaxed">
          {item.description}
        </p>
        
        <div className="flex flex-wrap gap-1 mt-3">
          {item.tags.map(tag => (
            <span
              key={tag}
              className="text-xs bg-gray-100/80 text-gray-700 px-2 py-1 rounded-full backdrop-blur-sm"
            >
              {tag}
            </span>
          ))}
        </div>
        
        <button
          className="w-full mt-4 bg-gradient-to-r from-primary-500 to-primary-600 text-white py-2 px-4 rounded-lg font-medium hover:from-primary-600 hover:to-primary-700 transform hover:scale-105 transition-all duration-200 shadow-md hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100"
          disabled={!item.available}
        >
          {item.available ? 'Add to Cart' : 'Out of Stock'}
        </button>
      </div>
    </div>
  );
};

export default MenuItem;