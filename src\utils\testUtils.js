import React from 'react';
import { render } from '@testing-library/react';

export const renderWithRouter = (ui, { route = '/' } = {}) => {
  window.history.pushState({}, 'Test page', route);
  return render(ui);
};

export const waitForAnimation = (duration = 500) => {
  return new Promise(resolve => setTimeout(resolve, duration));
};

export const mockIntersectionObserver = (isIntersecting = true) => {
  const mockObserver = {
    observe: jest.fn(),
    disconnect: jest.fn(),
    unobserve: jest.fn(),
  };

  global.IntersectionObserver = jest.fn().mockImplementation((callback) => {
    const entries = [{
      isIntersecting,
      intersectionRatio: isIntersecting ? 1 : 0,
      target: document.createElement('div'),
    }];
    
    setTimeout(() => callback(entries), 0);
    return mockObserver;
  });

  return mockObserver;
};

export const createMockMenuItem = (overrides = {}) => ({
  id: 'test-item-1',
  name: 'Test Coffee',
  description: 'A delicious test coffee',
  price: 4.99,
  category: 'coffee',
  image: 'https://example.com/coffee.jpg',
  available: true,
  tags: ['hot', 'caffeine'],
  ...overrides,
});

export const fireAnimationEnd = (element) => {
  const animationEndEvent = new Event('animationend');
  element.dispatchEvent(animationEndEvent);
};

export const fireTransitionEnd = (element) => {
  const transitionEndEvent = new Event('transitionend');
  element.dispatchEvent(transitionEndEvent);
};

export const hasAnimationClass = (element, className) => {
  return element.classList.contains(className);
};

export const getComputedAnimationProperty = (element, property) => {
  return window.getComputedStyle(element).getPropertyValue(property);
};