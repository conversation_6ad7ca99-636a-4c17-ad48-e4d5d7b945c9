import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import MenuItem from './MenuItem';
import { createMockMenuItem } from '../utils/testUtils';

// Mock the menuData service
jest.mock('../services/menuData', () => ({
  formatPrice: jest.fn((price) => `$${price.toFixed(2)}`),
  getPlaceholderImage: jest.fn((category) => `placeholder-${category}.jpg`),
}));

describe('MenuItem Component', () => {
  let user;
  let mockItem;

  beforeEach(() => {
    user = userEvent.setup();
    mockItem = createMockMenuItem();
    jest.clearAllMocks();
  });

  describe('Rendering Tests', () => {
    test('renders without crashing', () => {
      render(<MenuItem item={mockItem} />);
      expect(screen.getByText('Test Coffee')).toBeInTheDocument();
    });

    test('renders item name', () => {
      render(<MenuItem item={mockItem} />);
      
      expect(screen.getByText('Test Coffee')).toBeInTheDocument();
    });

    test('renders item description', () => {
      render(<MenuItem item={mockItem} />);
      
      expect(screen.getByText('A delicious test coffee')).toBeInTheDocument();
    });

    test('renders item price', () => {
      const { formatPrice } = require('../services/menuData');
      formatPrice.mockReturnValue('$4.99');
      
      render(<MenuItem item={mockItem} />);
      
      expect(screen.getByText('$4.99')).toBeInTheDocument();
    });

    test('renders item image', () => {
      render(<MenuItem item={mockItem} />);
      
      const image = screen.getByRole('img', { name: 'Test Coffee' });
      expect(image).toBeInTheDocument();
      expect(image).toHaveAttribute('src', 'https://example.com/coffee.jpg');
    });

    test('renders item tags', () => {
      render(<MenuItem item={mockItem} />);
      
      expect(screen.getByText('hot')).toBeInTheDocument();
      expect(screen.getByText('caffeine')).toBeInTheDocument();
    });

    test('renders add to cart button when available', () => {
      render(<MenuItem item={mockItem} />);
      
      const button = screen.getByRole('button', { name: /add to cart/i });
      expect(button).toBeInTheDocument();
      expect(button).not.toBeDisabled();
    });
  });

  describe('Animation and Styling Tests', () => {
    test('applies glassmorphic container styles', () => {
      render(<MenuItem item={mockItem} />);
      
      const container = screen.getByText('Test Coffee').closest('.group');
      expect(container).toHaveClass(
        'bg-white/70',
        'backdrop-blur-md',
        'rounded-xl',
        'border',
        'border-white/20',
        'shadow-lg'
      );
    });

    test('has hover animation classes', () => {
      render(<MenuItem item={mockItem} />);
      
      const container = screen.getByText('Test Coffee').closest('.group');
      expect(container).toHaveClass(
        'hover:shadow-xl',
        'transform',
        'hover:-translate-y-1',
        'transition-all',
        'duration-300',
        'hover:bg-white/80'
      );
    });

    test('has performance optimization with willChange', () => {
      render(<MenuItem item={mockItem} />);
      
      const container = screen.getByText('Test Coffee').closest('.group');
      expect(container).toHaveStyle({ willChange: 'opacity, transform' });
    });

    test('image has hover scale effect', () => {
      render(<MenuItem item={mockItem} />);
      
      const image = screen.getByRole('img', { name: 'Test Coffee' });
      expect(image).toHaveClass('group-hover:scale-105', 'transition-transform', 'duration-300');
    });

    test('title has color transition on hover', () => {
      render(<MenuItem item={mockItem} />);
      
      const title = screen.getByText('Test Coffee');
      expect(title).toHaveClass('group-hover:text-primary-600', 'transition-colors');
    });

    test('button has transform and transition effects', () => {
      render(<MenuItem item={mockItem} />);
      
      const button = screen.getByRole('button', { name: /add to cart/i });
      expect(button).toHaveClass(
        'transform',
        'hover:scale-105',
        'transition-all',
        'duration-200',
        'hover:shadow-lg'
      );
    });
  });

  describe('Unavailable Item Tests', () => {
    test('renders unavailable overlay when item is not available', () => {
      const unavailableItem = createMockMenuItem({ available: false });
      render(<MenuItem item={unavailableItem} />);
      
      expect(screen.getByText('Unavailable')).toBeInTheDocument();
    });

    test('renders disabled button when item is not available', () => {
      const unavailableItem = createMockMenuItem({ available: false });
      render(<MenuItem item={unavailableItem} />);
      
      const button = screen.getByRole('button', { name: /out of stock/i });
      expect(button).toBeDisabled();
      expect(button).toHaveClass('disabled:opacity-50', 'disabled:cursor-not-allowed');
    });

    test('unavailable overlay has proper styling', () => {
      const unavailableItem = createMockMenuItem({ available: false });
      render(<MenuItem item={unavailableItem} />);
      
      const overlay = screen.getByText('Unavailable').closest('.absolute');
      expect(overlay).toHaveClass('inset-0', 'bg-black/50', 'flex', 'items-center', 'justify-center');
    });
  });

  describe('Popular Item Tests', () => {
    test('renders popular badge when item has popular tag', () => {
      const popularItem = createMockMenuItem({ tags: ['hot', 'popular', 'caffeine'] });
      render(<MenuItem item={popularItem} />);
      
      expect(screen.getByText('Popular')).toBeInTheDocument();
    });

    test('popular badge has proper styling', () => {
      const popularItem = createMockMenuItem({ tags: ['hot', 'popular', 'caffeine'] });
      render(<MenuItem item={popularItem} />);
      
      const badge = screen.getByText('Popular');
      expect(badge).toHaveClass(
        'bg-gradient-to-r',
        'from-amber-400',
        'to-orange-500',
        'text-white',
        'text-xs',
        'font-bold'
      );
    });

    test('does not render popular badge when item is not popular', () => {
      render(<MenuItem item={mockItem} />);
      
      expect(screen.queryByText('Popular')).not.toBeInTheDocument();
    });
  });

  describe('Image Handling Tests', () => {
    test('handles image error by calling placeholder function', () => {
      const { getPlaceholderImage } = require('../services/menuData');
      render(<MenuItem item={mockItem} />);
      
      const image = screen.getByRole('img', { name: 'Test Coffee' });
      
      // Simulate image error
      fireEvent.error(image);
      
      expect(getPlaceholderImage).toHaveBeenCalledWith('coffee');
    });

    test('image has proper styling classes', () => {
      render(<MenuItem item={mockItem} />);
      
      const image = screen.getByRole('img', { name: 'Test Coffee' });
      expect(image).toHaveClass('w-full', 'h-48', 'object-cover');
    });
  });

  describe('Interactive Tests', () => {
    test('button is clickable when item is available', async () => {
      render(<MenuItem item={mockItem} />);
      
      const button = screen.getByRole('button', { name: /add to cart/i });
      await user.click(button);
      
      // Button should remain enabled after click
      expect(button).not.toBeDisabled();
    });

    test('button is not clickable when item is unavailable', async () => {
      const unavailableItem = createMockMenuItem({ available: false });
      render(<MenuItem item={unavailableItem} />);
      
      const button = screen.getByRole('button', { name: /out of stock/i });
      expect(button).toBeDisabled();
    });

    test('disabled button does not have hover scale effect', () => {
      const unavailableItem = createMockMenuItem({ available: false });
      render(<MenuItem item={unavailableItem} />);
      
      const button = screen.getByRole('button', { name: /out of stock/i });
      expect(button).toHaveClass('disabled:hover:scale-100');
    });
  });

  describe('Price Display Tests', () => {
    test('price is formatted correctly', () => {
      const { formatPrice } = require('../services/menuData');
      formatPrice.mockReturnValue('$4.99');
      
      render(<MenuItem item={mockItem} />);
      
      expect(formatPrice).toHaveBeenCalledWith(4.99);
      expect(screen.getByText('$4.99')).toBeInTheDocument();
    });

    test('price has proper styling', () => {
      const { formatPrice } = require('../services/menuData');
      formatPrice.mockReturnValue('$4.99');
      
      render(<MenuItem item={mockItem} />);
      
      const priceElement = screen.getByText('$4.99');
      expect(priceElement).toHaveClass(
        'text-lg',
        'font-bold',
        'text-primary-600',
        'bg-primary-50',
        'px-2',
        'py-1',
        'rounded-lg'
      );
    });
  });

  describe('Tag Display Tests', () => {
    test('all tags are rendered', () => {
      const multiTagItem = createMockMenuItem({
        tags: ['hot', 'caffeine', 'popular', 'organic']
      });
      render(<MenuItem item={multiTagItem} />);
      
      expect(screen.getByText('hot')).toBeInTheDocument();
      expect(screen.getByText('caffeine')).toBeInTheDocument();
      expect(screen.getByText('popular')).toBeInTheDocument();
      expect(screen.getByText('organic')).toBeInTheDocument();
    });

    test('tags have proper styling', () => {
      render(<MenuItem item={mockItem} />);
      
      const tag = screen.getByText('hot');
      expect(tag).toHaveClass(
        'text-xs',
        'bg-gray-100/80',
        'text-gray-700',
        'px-2',
        'py-1',
        'rounded-full',
        'backdrop-blur-sm'
      );
    });
  });

  describe('Layout Tests', () => {
    test('has proper spacing classes', () => {
      render(<MenuItem item={mockItem} />);
      
      const container = screen.getByText('Test Coffee').closest('.group');
      expect(container).toHaveClass('p-6');
    });

    test('image container has proper styling', () => {
      render(<MenuItem item={mockItem} />);
      
      const imageContainer = screen.getByRole('img').closest('.relative');
      expect(imageContainer).toHaveClass('overflow-hidden', 'rounded-lg', 'mb-4');
    });

    test('content has proper spacing', () => {
      render(<MenuItem item={mockItem} />);
      
      const contentContainer = screen.getByText('Test Coffee').closest('.space-y-2');
      expect(contentContainer).toBeInTheDocument();
    });
  });

  describe('Accessibility Tests', () => {
    test('image has proper alt text', () => {
      render(<MenuItem item={mockItem} />);
      
      const image = screen.getByRole('img', { name: 'Test Coffee' });
      expect(image).toHaveAttribute('alt', 'Test Coffee');
    });

    test('button has proper accessible text', () => {
      render(<MenuItem item={mockItem} />);
      
      const button = screen.getByRole('button', { name: /add to cart/i });
      expect(button).toBeInTheDocument();
    });

    test('unavailable button has proper accessible text', () => {
      const unavailableItem = createMockMenuItem({ available: false });
      render(<MenuItem item={unavailableItem} />);
      
      const button = screen.getByRole('button', { name: /out of stock/i });
      expect(button).toBeInTheDocument();
    });
  });

  describe('Edge Cases Tests', () => {
    test('handles empty tags array', () => {
      const noTagsItem = createMockMenuItem({ tags: [] });
      render(<MenuItem item={noTagsItem} />);
      
      expect(screen.getByText('Test Coffee')).toBeInTheDocument();
    });

    test('handles long item names', () => {
      const longNameItem = createMockMenuItem({
        name: 'Super Long Coffee Name That Might Break Layout'
      });
      render(<MenuItem item={longNameItem} />);
      
      expect(screen.getByText('Super Long Coffee Name That Might Break Layout')).toBeInTheDocument();
    });

    test('handles long descriptions', () => {
      const longDescItem = createMockMenuItem({
        description: 'This is a very long description that might test the layout and text wrapping capabilities of the component'
      });
      render(<MenuItem item={longDescItem} />);
      
      expect(screen.getByText(/This is a very long description/)).toBeInTheDocument();
    });
  });
});