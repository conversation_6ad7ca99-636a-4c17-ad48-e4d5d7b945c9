#!/usr/bin/env node

/**
 * Test script for WebCafeShop Task Automation
 * Verifies that all automation components work correctly
 */

const { execSync } = require('child_process');
const fs = require('fs');

class AutomationTester {
  constructor() {
    this.testResults = [];
    this.passed = 0;
    this.failed = 0;
  }

  log(message, type = 'info') {
    const timestamp = new Date().toISOString();
    const prefix = {
      'info': 'ℹ️',
      'success': '✅',
      'error': '❌',
      'warning': '⚠️'
    }[type] || 'ℹ️';
    
    console.log(`${timestamp} ${prefix} ${message}`);
  }

  async runTest(testName, testFunction) {
    this.log(`Running test: ${testName}`);
    
    try {
      await testFunction();
      this.testResults.push({ name: testName, status: 'PASSED' });
      this.passed++;
      this.log(`Test passed: ${testName}`, 'success');
    } catch (error) {
      this.testResults.push({ name: testName, status: 'FAILED', error: error.message });
      this.failed++;
      this.log(`Test failed: ${testName} - ${error.message}`, 'error');
    }
  }

  async testTaskManagerCLI() {
    // Test task manager CLI commands
    try {
      execSync('node task-manager.js list-tasks', { stdio: 'pipe' });
      this.log('Task manager CLI is working', 'success');
    } catch (error) {
      throw new Error('Task manager CLI failed: ' + error.message);
    }
  }

  async testTaskStatusUpdate() {
    // Test updating task status programmatically
    try {
      const result = execSync('node task-manager.js update-task "responsive-styling" "in-progress" "automation-test"', { 
        stdio: 'pipe',
        encoding: 'utf8'
      });
      
      if (result.includes('✅') || result.includes('updated')) {
        this.log('Task status update working', 'success');
      } else {
        throw new Error('Task status update did not return success message');
      }
    } catch (error) {
      // This might fail if task doesn't exist, which is okay for testing
      this.log('Task status update test completed (task may not exist)', 'warning');
    }
  }

  async testWebhookHandler() {
    // Test webhook handler startup
    try {
      // Test if webhook handler can be imported
      const WebhookHandler = require('./webhook-handler.js');
      if (typeof WebhookHandler === 'function') {
        this.log('Webhook handler module loads correctly', 'success');
      } else {
        throw new Error('Webhook handler is not a valid class');
      }
    } catch (error) {
      throw new Error('Webhook handler failed to load: ' + error.message);
    }
  }

  async testJestAutomation() {
    // Test Jest automation module
    try {
      const JestAutomation = require('./jest-automation.js');
      if (typeof JestAutomation === 'function') {
        this.log('Jest automation module loads correctly', 'success');
      } else {
        throw new Error('Jest automation is not a valid class');
      }
    } catch (error) {
      throw new Error('Jest automation failed to load: ' + error.message);
    }
  }

  async testTaskFileExists() {
    // Test if task file exists and is readable
    try {
      if (fs.existsSync('augment.taskplan.json')) {
        const data = fs.readFileSync('augment.taskplan.json', 'utf8');
        const tasks = JSON.parse(data);
        
        if (tasks.tasks && Array.isArray(tasks.tasks)) {
          this.log(`Task file contains ${tasks.tasks.length} tasks`, 'success');
        } else {
          throw new Error('Task file format is invalid');
        }
      } else {
        throw new Error('Task file does not exist');
      }
    } catch (error) {
      throw new Error('Task file test failed: ' + error.message);
    }
  }

  async testPackageScripts() {
    // Test if package.json has the required scripts
    try {
      const packageData = fs.readFileSync('package.json', 'utf8');
      const pkg = JSON.parse(packageData);
      
      const requiredScripts = [
        'test:automation',
        'test:ci',
        'task-manager',
        'webhook-server'
      ];
      
      const missingScripts = requiredScripts.filter(script => !pkg.scripts[script]);
      
      if (missingScripts.length === 0) {
        this.log('All required npm scripts are present', 'success');
      } else {
        throw new Error(`Missing npm scripts: ${missingScripts.join(', ')}`);
      }
    } catch (error) {
      throw new Error('Package scripts test failed: ' + error.message);
    }
  }

  async testDependencies() {
    // Test if required dependencies are installed
    try {
      const packageData = fs.readFileSync('package.json', 'utf8');
      const pkg = JSON.parse(packageData);
      
      const requiredDeps = ['express'];
      const allDeps = { ...pkg.dependencies, ...pkg.devDependencies };
      
      const missingDeps = requiredDeps.filter(dep => !allDeps[dep]);
      
      if (missingDeps.length === 0) {
        this.log('All required dependencies are installed', 'success');
      } else {
        throw new Error(`Missing dependencies: ${missingDeps.join(', ')}`);
      }
    } catch (error) {
      throw new Error('Dependencies test failed: ' + error.message);
    }
  }

  async testAutomationFiles() {
    // Test if all automation files exist
    const requiredFiles = [
      'task-manager.js',
      'webhook-handler.js',
      'jest-automation.js',
      'automation-setup.md'
    ];
    
    const missingFiles = requiredFiles.filter(file => !fs.existsSync(file));
    
    if (missingFiles.length === 0) {
      this.log('All automation files are present', 'success');
    } else {
      throw new Error(`Missing files: ${missingFiles.join(', ')}`);
    }
  }

  async runAllTests() {
    this.log('🚀 Starting WebCafeShop Automation Tests');
    this.log('==========================================');
    
    await this.runTest('Task Manager CLI', () => this.testTaskManagerCLI());
    await this.runTest('Task Status Update', () => this.testTaskStatusUpdate());
    await this.runTest('Webhook Handler Module', () => this.testWebhookHandler());
    await this.runTest('Jest Automation Module', () => this.testJestAutomation());
    await this.runTest('Task File Exists', () => this.testTaskFileExists());
    await this.runTest('Package Scripts', () => this.testPackageScripts());
    await this.runTest('Dependencies', () => this.testDependencies());
    await this.runTest('Automation Files', () => this.testAutomationFiles());
    
    this.printSummary();
  }

  printSummary() {
    this.log('==========================================');
    this.log('🏁 Test Summary');
    this.log(`Total Tests: ${this.testResults.length}`);
    this.log(`Passed: ${this.passed}`, 'success');
    this.log(`Failed: ${this.failed}`, this.failed > 0 ? 'error' : 'success');
    
    if (this.failed > 0) {
      this.log('\n❌ Failed Tests:');
      this.testResults
        .filter(test => test.status === 'FAILED')
        .forEach(test => {
          this.log(`  - ${test.name}: ${test.error}`, 'error');
        });
    }
    
    if (this.failed === 0) {
      this.log('\n🎉 All tests passed! Automation system is ready.', 'success');
      this.log('\nNext steps:');
      this.log('1. Start webhook server: npm run webhook-server');
      this.log('2. Configure GitHub webhook');
      this.log('3. Run tests with automation: npm run test:automation');
    } else {
      this.log('\n⚠️ Some tests failed. Please fix the issues before using automation.', 'warning');
    }
    
    process.exit(this.failed > 0 ? 1 : 0);
  }
}

// Run tests if this file is executed directly
if (require.main === module) {
  const tester = new AutomationTester();
  tester.runAllTests().catch(error => {
    console.error('❌ Test runner failed:', error.message);
    process.exit(1);
  });
}

module.exports = AutomationTester;
