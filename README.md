# WebCafeShop

A React-based web cafe shop application with Firebase authentication and hosting.

🚀 **Live Demo:** https://webcafeshop.web.app

## Getting Started

### Prerequisites
- Node.js and npm
- Firebase account and project setup

### Installation
1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```
3. Set up Firebase environment variables with `_REACT_APP_` prefix:
   - `_REACT_APP_API_KEY`
   - `_REACT_APP_AUTH_DOMAIN`
   - `_REACT_APP_PROJECT_ID`
   - `_REACT_APP_STORAGE_BUCKET`
   - `_REACT_APP_MESSAGING_SENDER_ID`
   - `_REACT_APP_APP_ID`
   - `_REACT_APP_MEASUREMENT_ID`

### Available Scripts

- `npm start` - Runs the app in development mode on [http://localhost:3000](http://localhost:3000)
- `npm test` - Launches the test runner in interactive watch mode
- `npm run build` - Builds the app for production to the `build` folder
- `npm run eject` - Ejects from Create React App (one-way operation)

## Features

- **Authentication**: Email/password sign-in and sign-up using Firebase Auth
- **Protected Routes**: Authenticated users access the main application, unauthenticated users see login
- **Firebase Integration**: Authentication and hosting configured
- **Responsive Design**: Mobile-friendly interface

## Architecture

- **Frontend**: React 19 with React Router for navigation
- **Authentication**: Firebase Auth with email/password
- **Hosting**: Firebase Hosting with SPA routing
- **State Management**: React hooks for local state

## Deployment

The app is configured for Firebase Hosting. The `firebase.json` file is set up to serve the built application with proper SPA routing.