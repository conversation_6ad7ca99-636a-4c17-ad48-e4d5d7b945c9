import React, {useState, useMemo} from 'react';
import MenuItem from './MenuItem';
import CategoryFilter from './CategoryFilter';
import SearchBar from './SearchBar';
import {getItemsByCategory, searchItems} from '../services/menuData';

const MenuSection = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  const filteredItems = useMemo(() => {
    let items = getItemsByCategory(activeCategory);
    
    if (searchQuery.trim()) {
      items = searchItems(searchQuery).filter(item => 
        activeCategory === 'all' || item.category === activeCategory
      );
    }
    
    return items;
  }, [activeCategory, searchQuery]);

  const handleCategoryChange = (categoryId) => {
    setActiveCategory(categoryId);
  };

  const handleSearchChange = (query) => {
    setSearchQuery(query);
  };

  return (
    <section id="features" className="py-16 px-4 sm:px-6 lg:px-8">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-4xl font-bold text-gray-900 mb-4">
            Our Menu
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            Discover our carefully crafted selection of premium coffee, fresh pastries, and delicious light meals
          </p>
        </div>

        <CategoryFilter
          activeCategory={activeCategory}
          onCategoryChange={handleCategoryChange}
        />

        <SearchBar
          onSearchChange={handleSearchChange}
          placeholder="Search our delicious menu..."
        />

        {filteredItems.length === 0 ? (
          <div className="text-center py-12">
            <div className="bg-white/70 backdrop-blur-md rounded-xl p-8 border border-white/20 shadow-lg">
              <svg className="mx-auto h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.172 16.172a4 4 0 015.656 0M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <h3 className="text-lg font-medium text-gray-900 mb-2">No items found</h3>
              <p className="text-gray-600">Try adjusting your search or filter to find what you're looking for.</p>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredItems.map(item => (
              <MenuItem key={item.id} item={item} />
            ))}
          </div>
        )}

        {filteredItems.length > 0 && (
          <div className="text-center mt-12">
            <p className="text-gray-600 bg-white/50 backdrop-blur-md rounded-lg px-4 py-2 inline-block border border-white/20">
              Showing {filteredItems.length} {filteredItems.length === 1 ? 'item' : 'items'}
            </p>
          </div>
        )}
      </div>
    </section>
  );
};

export default MenuSection;