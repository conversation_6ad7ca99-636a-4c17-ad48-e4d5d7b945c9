services:
  mcp-toolkit-ui:
    build:
      context: .
      dockerfile: Dockerfile.toolkit
    container_name: mcp-toolkit-ui
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://localhost:3002
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
      - ./.codes.json:/app/.codes.json:ro
    depends_on:
      - firecrawl-mcp
    networks:
      - mcp-network

  firecrawl-mcp:
    build:
      context: .
      dockerfile: Dockerfile.firecrawl
    container_name: firecrawl-mcp
    ports:
      - "3002:3000"
    environment:
      - MCP_SERVER_NAME=firecrawl
      - FIRECRAWL_API_KEY=${FIRECRAWL_API_KEY:-}
    volumes:
      - ./.codes.json:/usr/src/app/.codes.json:ro
    networks:
      - mcp-network

  git-mcp:
    image: mcp/git
    container_name: git-mcp-new
    environment:
      - MCP_SERVER_NAME=git
    volumes:
      - .:/workspace:ro
    networks:
      - mcp-network

networks:
  mcp-network:
    driver: bridge