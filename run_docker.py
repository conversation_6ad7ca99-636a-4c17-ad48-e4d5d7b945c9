#!/usr/bin/env python3
"""
Docker automation script for React + Firebase project.
Builds and runs Docker containers with proper error handling.
"""

import subprocess
import sys
import os


def run_command(command, description):
    """Run a shell command and handle errors."""
    print(f"\n🔄 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed successfully")
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed:")
        print(f"Error: {e.stderr}")
        return False


def check_docker():
    """Check if Docker is installed and running."""
    print("🔍 Checking Docker installation...")
    
    # Check if Docker is installed
    try:
        subprocess.run(["docker", "--version"], check=True, capture_output=True)
        print("✅ Docker is installed")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ Docker is not installed or not in PATH")
        print("Please install Docker Desktop from https://www.docker.com/products/docker-desktop")
        return False
    
    # Check if Docker daemon is running
    try:
        subprocess.run(["docker", "info"], check=True, capture_output=True)
        print("✅ Docker daemon is running")
        return True
    except subprocess.CalledProcessError:
        print("❌ Docker daemon is not running")
        print("Please start Docker Desktop")
        return False


def check_files():
    """Check if required Docker files exist."""
    required_files = ["Dockerfile", "docker-compose.yml"]
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ Missing required files: {', '.join(missing_files)}")
        return False
    
    print("✅ All required Docker files found")
    return True


def main():
    """Main execution function."""
    print("🚀 Starting Docker build and run process...")
    
    # Check prerequisites
    if not check_docker():
        sys.exit(1)
    
    if not check_files():
        sys.exit(1)
    
    # Build Docker image
    if not run_command("docker build -t webcafeshop .", "Building Docker image"):
        sys.exit(1)
    
    # Start services with docker-compose
    if not run_command("docker-compose up -d", "Starting Docker services"):
        sys.exit(1)
    
    print("\n🎉 Docker containers are now running!")
    print("📋 Use 'docker-compose logs -f' to view logs")
    print("🛑 Use 'docker-compose down' to stop services")


if __name__ == "__main__":
    main()