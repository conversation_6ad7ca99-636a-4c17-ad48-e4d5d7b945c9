<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebCafeShop - Task Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">WebCafeShop Task Dashboard</h1>
            <p class="text-gray-600">Coffee Menu System Development Progress</p>
            <div class="mt-4 flex items-center space-x-4">
                <span class="bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium">Phase 1: Core Menu System</span>
                <span class="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium">46% Complete</span>
            </div>
        </div>

        <!-- Progress Overview -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Total Tasks</h3>
                <p class="text-3xl font-bold text-blue-600" id="totalTasks">13</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Completed</h3>
                <p class="text-3xl font-bold text-green-600" id="completedTasks">6</p>
            </div>
            <div class="bg-white rounded-lg shadow-md p-6">
                <h3 class="text-lg font-semibold text-gray-800 mb-2">Remaining</h3>
                <p class="text-3xl font-bold text-orange-600" id="remainingTasks">7</p>
            </div>
        </div>

        <!-- Progress Chart -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Progress Overview</h3>
            <div class="w-full bg-gray-200 rounded-full h-4 mb-4">
                <div class="bg-blue-600 h-4 rounded-full transition-all duration-300" style="width: 46%"></div>
            </div>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                    <span>Completed: 6</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-yellow-500 rounded-full mr-2"></div>
                    <span>In Progress: 0</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-gray-400 rounded-full mr-2"></div>
                    <span>Todo: 7</span>
                </div>
                <div class="flex items-center">
                    <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                    <span>Blocked: 0</span>
                </div>
            </div>
        </div>

        <!-- Current Phase Tasks -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Phase 1: Core Menu System</h3>
            <div class="space-y-3">
                <div class="flex items-center p-3 bg-green-50 rounded-lg">
                    <span class="text-green-600 mr-3">✅</span>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800">Menu Data Service</h4>
                        <p class="text-sm text-gray-600">Built src/services/menuData.js with coffee menu structure</p>
                    </div>
                    <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">COMPLETED</span>
                </div>
                
                <div class="flex items-center p-3 bg-green-50 rounded-lg">
                    <span class="text-green-600 mr-3">✅</span>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800">MenuItem Component</h4>
                        <p class="text-sm text-gray-600">Individual menu item cards with image, name, price, description</p>
                    </div>
                    <span class="text-xs bg-green-100 text-green-800 px-2 py-1 rounded">COMPLETED</span>
                </div>

                <div class="flex items-center p-3 bg-gray-50 rounded-lg">
                    <span class="text-gray-400 mr-3">⏳</span>
                    <div class="flex-1">
                        <h4 class="font-medium text-gray-800">Responsive Design & Mobile Optimization</h4>
                        <p class="text-sm text-gray-600">Implement mobile-first responsive design with smooth transitions</p>
                    </div>
                    <span class="text-xs bg-gray-100 text-gray-800 px-2 py-1 rounded">TODO</span>
                </div>
            </div>
        </div>

        <!-- Next Phase Preview -->
        <div class="bg-white rounded-lg shadow-md p-6">
            <h3 class="text-lg font-semibold text-gray-800 mb-4">Phase 2: Advanced Features (Upcoming)</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="p-4 border border-gray-200 rounded-lg">
                    <h4 class="font-medium text-gray-800 mb-2">🛒 Shopping Cart System</h4>
                    <p class="text-sm text-gray-600">Add to cart, quantity management, localStorage persistence</p>
                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded mt-2 inline-block">HIGH PRIORITY</span>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-lg">
                    <h4 class="font-medium text-gray-800 mb-2">📋 Order Management</h4>
                    <p class="text-sm text-gray-600">Order placement, history, and status tracking</p>
                    <span class="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded mt-2 inline-block">HIGH PRIORITY</span>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-lg">
                    <h4 class="font-medium text-gray-800 mb-2">🔥 Firebase Integration</h4>
                    <p class="text-sm text-gray-600">Firestore for menu data, orders, and user preferences</p>
                    <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded mt-2 inline-block">MEDIUM PRIORITY</span>
                </div>
                
                <div class="p-4 border border-gray-200 rounded-lg">
                    <h4 class="font-medium text-gray-800 mb-2">💳 Payment Integration</h4>
                    <p class="text-sm text-gray-600">Stripe/PayPal integration for order completion</p>
                    <span class="text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded mt-2 inline-block">MEDIUM PRIORITY</span>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-6 text-center">
            <p class="text-gray-600 mb-4">Quick Actions</p>
            <div class="space-x-4">
                <button onclick="openTaskManager()" class="bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Open Task Manager
                </button>
                <button onclick="viewProject()" class="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors">
                    View Live Project
                </button>
                <button onclick="refreshData()" class="bg-gray-600 text-white px-4 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                    Refresh Data
                </button>
            </div>
        </div>
    </div>

    <script>
        function openTaskManager() {
            alert('Run: node task-manager.js in your terminal to open the interactive task manager');
        }

        function viewProject() {
            window.open('https://webcafeshop.web.app', '_blank');
        }

        function refreshData() {
            location.reload();
        }

        // Auto-refresh every 5 minutes
        setInterval(refreshData, 300000);
    </script>
</body>
</html>
