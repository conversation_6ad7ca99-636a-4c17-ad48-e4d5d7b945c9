// Example: Protected Routes Pattern with Firebase Auth
import React, { useEffect, useState } from "react";
import { onAuthStateChanged } from "firebase/auth";
import { auth } from "../firebase";

// App Component with Authentication Flow
const App = () => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      setUser(user);
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="App">
      {user ? (
        // Protected content for authenticated users
        <HomePage user={user} />
      ) : (
        // Public content for unauthenticated users
        <Login />
      )}
    </div>
  );
};

// Protected Component Example
const ProtectedComponent = ({ user, children }) => {
  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">Access Restricted</h2>
          <p className="text-gray-600">Please log in to access this content.</p>
        </div>
      </div>
    );
  }

  return children;
};

export { App, ProtectedComponent };