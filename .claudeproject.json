{"name": "WebCafeShop", "description": "React-based coffee shop web application with Firebase auth", "version": "1.0.0", "framework": "React", "styling": "Tailwind CSS", "backend": "Firebase", "deployment": "Firebase Hosting", "structure": {"components": "src/components/", "services": "src/services/", "views": "src/Views/", "assets": "src/assets/"}, "rules": {"components": "Functional components with arrow functions", "styling": "Tailwind CSS utility classes only", "state": "React hooks (useState, useEffect, useMemo)", "auth": "Firebase v9+ modular SDK", "commits": "feat:, fix:, chore: prefixes"}}